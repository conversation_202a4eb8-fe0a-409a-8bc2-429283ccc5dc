using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PBC.HelpdeskService.Models;


namespace PBC.HelpdeskService.Services
{
    public class HelpDeskServiceRequestServices : IHelpDeskServiceRequestServices
    {

        private readonly IUtilityServiceClient _utilityServiceClient;

        private readonly IConfiguration _configuration;

        static bool IsSendPCDMail = false;
        private readonly IWorkflowServiceClient _workflowServiceClient;

        private readonly ILogger<HelpDeskUserLandingPageServices> _logger;
        public static string AppPath = "quest-partsassist\\EPC_UploadedFiles";

        public HelpDeskServiceRequestServices(IUtilityServiceClient utilityServiceClient, ILogger<HelpDeskUserLandingPageServices> logger, IConfiguration configuration)
        {
            _utilityServiceClient = utilityServiceClient;
            _logger = logger;
            _configuration = configuration;
        }

        #region ::: InsertServiceRequestNegativeFeedbackEmails :::
        /// <Purpose >"Insert Service Request Negative Feedback Emails"</Purpose>
        /// <returns></returns> 
        /// <summary>
        /// Insert Service Request Negative Feedback Emails
        /// </summary>
        /// <returns>...</returns>
        public async Task<IActionResult> InsertServiceRequestNegativeFeedbackEmailsAsync(InsertServiceRequestNegativeFeedbackEmailsList Obj, string constring, int LogException)
        {
            StringBuilder Body = new StringBuilder();
            string BranchName = string.Empty;
            string CompanyName = string.Empty;
            string Model = string.Empty;
            string EmailID = string.Empty;
            string EmailSub = string.Empty;
            string EmailBody = string.Empty;
            IActionResult result;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Get EmailID
                    string emailQuery = @"
                    SELECT Param_value 
                    FROM GNM_CompParam 
                    WHERE Company_ID = @Company_ID 
                    AND UPPER(Param_Name) = 'NEGATIVEFEEDBACKEMAILID'";

                    using (SqlCommand emailCmd = new SqlCommand(emailQuery, connection))
                    {
                        emailCmd.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                        EmailID = Convert.ToString(emailCmd.ExecuteScalar());
                    }

                    if (!string.IsNullOrWhiteSpace(EmailID))
                    {
                        // Fetch BranchName
                        string branchQuery = @"
                    SELECT Branch_Name 
                    FROM GNM_Branch 
                    WHERE Company_ID = @Company_ID 
                      AND Branch_ID = @Branch_ID";

                        using (SqlCommand branchCmd = new SqlCommand(branchQuery, connection))
                        {
                            branchCmd.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                            branchCmd.Parameters.AddWithValue("@Branch_ID", Obj.Branch_ID);
                            BranchName = Convert.ToString(branchCmd.ExecuteScalar());
                        }

                        // Fetch CompanyName
                        string companyQuery = @"
                    SELECT Company_Name 
                    FROM GNM_Company 
                    WHERE Company_ID = @Company_ID";

                        using (SqlCommand companyCmd = new SqlCommand(companyQuery, connection))
                        {
                            companyCmd.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                            CompanyName = Convert.ToString(companyCmd.ExecuteScalar());
                        }

                        // Fetch Model Name
                        if (Obj.Model_Id > 0)
                        {
                            string modelQuery = @"
                        SELECT Model_Name 
                        FROM GNM_Model 
                        WHERE Model_ID = @Model_ID";

                            using (SqlCommand modelCmd = new SqlCommand(modelQuery, connection))
                            {
                                modelCmd.Parameters.AddWithValue("@Model_ID", Obj.Model_Id);
                                Model = Convert.ToString(modelCmd.ExecuteScalar());
                            }
                        }



                        // Prepare Email Template

                        // Initialize the CommonMethodForEmailandSMSList object
                        CommonMethodForEmailandSMSList emailData = new CommonMethodForEmailandSMSList
                        {
                            TemplateCode = "NegativeFeedback",
                            CompanyId = Obj.Company_ID,
                            LanguageCode = "",
                            BranchId = Obj.Branch_ID,
                            p1 = Obj.Customer,
                            p2 = Obj.RequestNumber,
                            p3 = Obj.ProductType,
                            p4 = Model,
                            p5 = Obj.SerialNumber,
                            p6 = Obj.ContactPerson,
                            p7 = Obj.MobileNumber,
                            p8 = Obj.CallDescription,
                            p9 = Obj.CustomerRating,
                            p10 = Obj.Feedback,
                            p11 = BranchName,
                            p12 = CompanyName,
                            p13 = "",
                            p14 = "",
                            p15 = "",
                            p16 = "",
                            p17 = "",
                            p18 = "",
                            p19 = "",
                            p20 = ""
                        };

                        // Call the CommonMethodForEmailandSMS using the emailData object
                        var templateDetails = CommonMethodForEmailandSMS(constring, emailData);

                        // Assign the subject, body, cc, and bcc from the templateDetails array
                        EmailSub = templateDetails[0]?.ToString();
                        EmailBody = templateDetails[1]?.ToString().Replace("\n", "<br/>");
                        string emailCC = templateDetails[4]?.ToString();
                        string emailBCC = templateDetails[3]?.ToString();


                        // Insert Email Record
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertEmailQueue", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@Email_Subject", EmailSub);
                                cmd.Parameters.AddWithValue("@Email_Body", EmailBody);
                                cmd.Parameters.AddWithValue("@Email_cc", emailCC ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Email_Bcc", emailBCC ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Email_Queue_Date", DateTime.Now);
                                cmd.Parameters.AddWithValue("@Email_To", EmailID);

                                conn.Open();
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }
                    result = new OkObjectResult("Email queued successfully.");
                }
            }
            catch (Exception e)
            {
                if (LogException == 1)
                {
                    //LS.LogSheetExporter.LogToTextFile(
                    //    e.HResult, e.GetType().FullName + ":" + e.Message,
                    //    e.TargetSite.ToString(), e.StackTrace
                    //);
                }
                result = new BadRequestObjectResult($"An error occurred: {e.Message}");
            }
            return new JsonResult(result);
        }

        #endregion


        // ==================================================================
        // PYD's Area
        public async Task<JsonResult> InsertSR(string Reopen, string Data, int BranchID, string UserLanguageCode, List<string> RequestParams,
            List<Attachements> HDAttachmentData, string Path, string connString, int LogException, int Company_ID, int User_ID
            , string HelpDesk, string HelpLineNumber, int Language_ID, int Employee_ID, int MenuID, string HolidayDetails
            , bool isFromWebAPI = false)
        {
            AppPath = Path;
            int CompanyID = Company_ID;
            int UserID = User_ID;
            string NotesData = RequestParams[0];
            string PartsDetailData = RequestParams[1];
            string TMLPartsDetailData = RequestParams[2];
            string FollowUpsDetailData = RequestParams[3];
            string ProductDetailsData = RequestParams[4];
            //string HDAttachmentData = RequestParams[5];
            string ContactPersonObj = RequestParams[5];
            string AttachmentsData = RequestParams[6];
            string UserCulture = RequestParams[7];
            string ReLoginView = RequestParams[8];
            string Attachmentdel = RequestParams[9];
            int objid = Convert.ToInt32(RequestParams[10]);
            DateTime Loggindate = Convert.ToDateTime(RequestParams[11]);
            string QuestionData = RequestParams[14];
            string SMStoAssigneeText = string.Empty;
            string SMStoCustomerText = string.Empty;
            string CustomerMobNo = string.Empty;
            string CustomerEmailID = string.Empty;
            string EmailSub = string.Empty;
            string EmailtoAssigneeBody = string.Empty;
            string EmailtoCustomerBody = string.Empty;
            string AddresseEmailBCC = string.Empty;
            string AddresseCC = string.Empty;
            string CustBCC = string.Empty;
            string CustCC = string.Empty;
            int ProductCustomerid = Convert.ToInt32(RequestParams[12].ToString());
            //HelpDesk.Controllers.HelpDeskServiceRequestController HelpDeskControllerObj = new Controllers.HelpDeskServiceRequestController();
            JTokenReader jr = null;
            GNM_PartyContactPersonDetails partyContact = new GNM_PartyContactPersonDetails();
            string NextStepType = string.Empty;
            int financialYear = DateTime.Now.Year;
            int WorkFlowID = await _utilityServiceClient.GetWorkFlowIDAsync("Case Registration", HelpDesk, connString, LogException);
            int CaseType_ID = 0;
            int CurrentStepID = 0;
            int NextStepID = 0;
            int ActionID = 0;
            int AssignedTo = 0;
            string ActionRemarks = string.Empty;
            string NextStapName = string.Empty;

            int PCurrentStepID = 0;
            int PNextStepID = 0;
            int PActionID = 0;
            int PAssignedTo = 0;
            string PActionRemarks = string.Empty;
            string PNextStapName = string.Empty;
            byte PAddresseType = 1;
            int PRoleID = 0;
            int PCallStatus_ID = 0;

            byte AddresseType = 1;
            int RoleID = 0;
            string ContactPartyName = string.Empty;
            int CallStatus_ID = 0;
            int ComplaintNumber = 0;
            int UnRegSRID = 0;
            var jsonResult = default(dynamic);
            string ObjectName = "";
            JObject jObjx = null;
            JObject jobjy = null;
            JObject jobjz = null;
            JObject jobcp = null;
            JObject jobjPro = null;
            JObject jobTMlPartsList = null;
            JObject jobjQ = null;
            SMSTemplate smsCustomer = new SMSTemplate();
            SMSTemplate smsAssignee = new SMSTemplate();
            List<HD_ServiceRequest> ServiceRequestList = new List<HD_ServiceRequest>();
            // List<CoreProductMasterServices.GNM_Branch> BranchList = new List<CoreProductMasterServices.GNM_Branch>();
            List<GNM_Branch> BranchList = new List<GNM_Branch>();
            List<GNM_PartyContactPersonDetails> PartyContactPersonDetailsList = new List<GNM_PartyContactPersonDetails>();
            List<GNM_CompParam> CompParamList = new List<GNM_CompParam>();
            List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();
            List<WF_WFField> WFFieldList = new List<WF_WFField>();
            List<GNM_RefMaster> refMasterDetail = new List<GNM_RefMaster>();
            List<GNM_Product> ProductList = new List<GNM_Product>();
            List<WF_WFStepLink> WFStepLinkList = new List<WF_WFStepLink>();
            List<GNM_ProductStatusHistory> ProductStatusHistoryList = new List<GNM_ProductStatusHistory>();
            List<HD_UnregisteredServiceRequest> UnregisteredServiceRequestList = new List<HD_UnregisteredServiceRequest>();
            List<GNM_ProductCustomer> ProductCustomerList = new List<GNM_ProductCustomer>();
            
            try
            {
                //using (System.Transactions.TransactionScope scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required))
                //{
                //GNM_User User = new GNM_User();
                //User.Company_ID = CompanyID;
                //  User.User_ID = UserID;
                //int CompanyID = User.Company_ID;
                //int UserID = User.User_ID;
                //int BranchID = Convert.ToInt32(Session["Branch"].ToString());

                if (await _workflowServiceClient.CheckPrefixSuffixAsync(CompanyID, BranchID, "HelpDeskServiceRequest", HelpDesk, connString))
                {
                    HD_ServiceRequest hSR = JObject.Parse(Data).ToObject<HD_ServiceRequest>();
                    HD_ServiceRequest PhSR = JObject.Parse(Data).ToObject<HD_ServiceRequest>();
                    JObject jObj = JObject.Parse(Data);

                    if (NotesData != "")
                    {
                        jObjx = JObject.Parse(NotesData);
                    }   

                    if (PartsDetailData != "")
                    {
                        jobjy = JObject.Parse(PartsDetailData);
                    }
                    if (TMLPartsDetailData != "")
                    {
                        jobTMlPartsList = JObject.Parse(TMLPartsDetailData);
                    }
                    if (FollowUpsDetailData != "")
                    {
                        jobjz = JObject.Parse(FollowUpsDetailData);
                    }
                    if (ProductDetailsData != "")
                    {
                        jobjPro = JObject.Parse(ProductDetailsData);
                    }
                    if (QuestionData != "")
                    {
                        jobjQ = JObject.Parse(QuestionData);
                    }

                    hSR.ServiceRequestNumber = "";
                    hSR.ServiceRequestDate = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                    hSR.CallStatus_ID = 0;
                    hSR.ParentIssue_ID = hSR.ParentIssue_ID == 0 ? null : hSR.ParentIssue_ID;

                    hSR.Product_Unique_Number = await _utilityServiceClient.DecryptStringAsync(hSR.Product_Unique_Number);
                    hSR.Party_ID = hSR.Party_ID;
                    hSR.PartyContactPerson_ID = hSR.PartyContactPerson_ID;

                    hSR.Model_ID = (hSR.Model_ID == 0) ? null : hSR.Model_ID;
                    hSR.ContractorID = hSR.ContractorID == 0 ? null : hSR.ContractorID;
                    hSR.ContractorContactPerson_ID = hSR.ContractorContactPerson_ID == 0 ? null : hSR.ContractorContactPerson_ID;
                    hSR.Brand_ID = (hSR.Brand_ID == 0) ? null : hSR.Brand_ID;
                    hSR.ProductType_ID = (hSR.ProductType_ID == 0) ? null : hSR.ProductType_ID;
                    hSR.SerialNumber = hSR.SerialNumber;
                    hSR.Product_ID = getProductID(hSR.SerialNumber, hSR.Model_ID, connString, LogException);
                    hSR.ProductReading = (hSR.ProductReading == 0) ? null : hSR.ProductReading;
                    hSR.IsUnderWarranty = hSR.IsUnderWarranty;
                    hSR.IsDealer = Convert.ToBoolean(hSR.IsDealer);
                    hSR.CallMode_ID = hSR.CallMode_ID;
                    hSR.CallPriority_ID = hSR.CallPriority_ID;
                    hSR.CallComplexity_ID = hSR.CallComplexity_ID;
                    hSR.CallOwner_ID = (hSR.CallOwner_ID == 0) ? null : hSR.CallOwner_ID;
                    hSR.IsDealerList = hSR.IsDealerList;
                    hSR.ProductRateType = hSR.ProductRateType;


                    using (var conn = new SqlConnection(connString))
                    {
                        conn.Open();

                        string query = "SELECT * FROM GNM_RefMasterDetail";

                        using (var cmd = new SqlCommand(query, conn))
                        {
                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var refMasterDetailObj = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),
                                        RefMasterDetail_Short_Name = reader["RefMasterDetail_Short_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Short_Name"].ToString(),
                                        RefMasterDetail_IsActive = (Boolean)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (Boolean?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                    };

                                    refDetail.Add(refMasterDetailObj);
                                }
                            }
                        }
                    }
                    hSR.ScheduledType_ID = hSR.ScheduledType_ID == 0 ? refDetail.Where(a => a.RefMasterDetail_Short_Name == "FNS").Select(a => a.RefMasterDetail_ID).FirstOrDefault() : hSR.ScheduledType_ID;
                    jr = new JTokenReader(jObj["UnRegSRID"]);
                    jr.Read();
                    UnRegSRID = Convert.ToInt32(jr.Value);

                    jr = new JTokenReader(jObj["CallDateAndTime"]);
                    jr.Read();
                    hSR.CallDateAndTime = Convert.ToDateTime(jr.Value);
                    IsSendPCDMail = hSR.PromisedCompletionDate == null ? false : true;
                    if (hSR.PromisedCompletionDate != null)
                    {
                        if (Convert.ToDateTime(hSR.PromisedCompletionDate).Year != 1)
                        {
                            hSR.PromisedCompletionDate = Convert.ToDateTime(hSR.PromisedCompletionDate);// == "" ? null : hSR.PromisedCompletionDate;
                        }
                    }
                    if (hSR.ExpectedArrivalDateTime != null)
                    {
                        hSR.ExpectedArrivalDateTime = Convert.ToDateTime(hSR.ExpectedArrivalDateTime);
                    }
                    if (hSR.ExpectedDepartureDateTime != null)
                    {
                        hSR.ExpectedDepartureDateTime = Convert.ToDateTime(hSR.ExpectedDepartureDateTime);
                    }
                    hSR.Region_ID = (hSR.Region_ID == 0) ? null : hSR.Region_ID;

                    hSR.CallDescription = await _utilityServiceClient.DecryptStringAsync(hSR.CallDescription);
                    if (hSR.CaseType_ID == 4)
                    {
                        hSR.IssueArea_ID = null;
                        hSR.IssueSubArea_ID = null;
                        hSR.FunctionGroup_ID = null;
                    }
                    else
                    {
                        hSR.IssueArea_ID = hSR.IssueArea_ID == 0 ? null : hSR.IssueArea_ID;
                        hSR.IssueSubArea_ID = hSR.IssueSubArea_ID == 0 ? null : hSR.IssueSubArea_ID;
                        hSR.FunctionGroup_ID = (hSR.FunctionGroup_ID == 0) ? null : hSR.FunctionGroup_ID;
                    }
                    hSR.IsUnderBreakDown = hSR.IsUnderBreakDown;

                    hSR.QuestionaryLevel1_ID = hSR.QuestionaryLevel1_ID == 0 ? null : hSR.QuestionaryLevel1_ID;
                    hSR.QuestionaryLevel2_ID = hSR.QuestionaryLevel2_ID == 0 ? null : hSR.QuestionaryLevel2_ID;
                    hSR.QuestionaryLevel3_ID = hSR.QuestionaryLevel3_ID == 0 ? null : hSR.QuestionaryLevel3_ID;
                    hSR.DefectGroup_ID = hSR.DefectGroup_ID == 0 ? null : hSR.DefectGroup_ID;
                    hSR.DefectName_ID = hSR.DefectName_ID == 0 ? null : hSR.DefectName_ID;

                    hSR.RootCause = await _utilityServiceClient.DecryptStringAsync(hSR.RootCause) == "" ? null : await _utilityServiceClient.DecryptStringAsync(hSR.RootCause);

                    jr = new JTokenReader(jObj["ClosureDateTime"]);
                    jr.Read();
                    hSR.CallClosureDateAndTime = null;
                    hSR.ClosureType_ID = hSR.ClosureType_ID == 0 ? null : hSR.ClosureType_ID; //null;
                    hSR.ClosingDescription = await _utilityServiceClient.DecryptStringAsync(hSR.ClosingDescription) == "" ? null : await _utilityServiceClient.DecryptStringAsync(hSR.ClosingDescription);
                    hSR.InformationCollected = await _utilityServiceClient.DecryptStringAsync(hSR.InformationCollected) == "" ? null : await _utilityServiceClient.DecryptStringAsync(hSR.InformationCollected);

                    hSR.Company_ID = CompanyID;
                    hSR.Branch_ID = BranchID;
                    hSR.ModifiedDate = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                    hSR.ModifiedBy_ID = UserID;

                    hSR.Document_no = null;
                    hSR.CaseType_ID = hSR.CaseType_ID;
                    CaseType_ID = hSR.CaseType_ID;
                    hSR.CustomerRating = hSR.CustomerRating;
                    if (hSR.CustomerRating != -1)
                        hSR.CustomerFeedbackDate = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                    else
                        hSR.CustomerFeedbackDate = null;
                    hSR.IsNegetiveFeedback = hSR.IsNegetiveFeedback;
                    hSR.Flexi1 = hSR.Flexi1 == "" ? null : await _utilityServiceClient.DecryptStringAsync(hSR.Flexi1);
                    hSR.Flexi2 = hSR.Flexi2 == "" ? null : await _utilityServiceClient.DecryptStringAsync(hSR.Flexi2);
                    hSR.CallOwner_ID = (hSR.CallOwner_ID) == 0 ? null : hSR.CallOwner_ID;

                    hSR.EnquiryStage_ID = hSR.EnquiryStage_ID == 0 ? null : hSR.EnquiryStage_ID;
                    hSR.SalesOrderNumber = hSR.SalesOrderNumber == "" ? null : await _utilityServiceClient.DecryptStringAsync(hSR.SalesOrderNumber);
                    hSR.SalesOrderDate = hSR.SalesOrderDate.ToString() == "" ? null : hSR.SalesOrderDate;

                    hSR.ChildTicket_Sequence_ID = hSR.ChildTicket_Sequence_ID;

                    jr = new JTokenReader(jObj["CurrentStepID"]);
                    jr.Read();
                    CurrentStepID = Convert.ToInt32(jr.Value);

                    jr = new JTokenReader(jObj["ActionID"]);
                    jr.Read();
                    ActionID = Convert.ToInt32(jr.Value);

                    if (ActionID > 0)
                    {
                        jr = new JTokenReader(jObj["AssignTo"]);
                        jr.Read();
                        AssignedTo = Convert.ToInt32(jr.Value);

                        jr = new JTokenReader(jObj["AddresseType"]);
                        jr.Read();
                        AddresseType = Convert.ToByte(jr.Value);

                        jr = new JTokenReader(jObj["ActionRemarks"]);
                        jr.Read();
                        ActionRemarks = await _utilityServiceClient.DecryptStringAsync(jr.Value.ToString());
                    }

                    var NextStepResult = GetNextStepType(CompanyID, WorkFlowID, CurrentStepID, ActionID, connString);
                    JObject jObject = JObject.Parse(NextStepResult.ToString());
                    jr = new JTokenReader(jObject["NextStepID"]);
                    jr.Read();
                    NextStepID = Convert.ToInt32(jr.Value);

                    jr = new JTokenReader(jObject["NextStepType"]);
                    jr.Read();
                    NextStapName = jr.Value.ToString();

                    jr = new JTokenReader(jObject["RoleID"]);
                    jr.Read();
                    RoleID = Convert.ToInt32(jr.Value);

                    jr = new JTokenReader(jObject["ToStepStatus"]);
                    jr.Read();
                    CallStatus_ID = Convert.ToInt32(jr.Value);

                    hSR.CallStatus_ID = CallStatus_ID;
                    hSR.ActionRemarks = ActionRemarks;



                    //-----------------------------Parent Work Flow Details Save START -----------------------------------------

                    //jr = new JTokenReader(jObj["PCurrentStepID"]);
                    //jr.Read();
                    //PCurrentStepID = Convert.ToInt32(jr.Value);

                    //jr = new JTokenReader(jObj["PActionID"]);
                    //jr.Read();
                    //PActionID = Convert.ToInt32(jr.Value);

                    //if (PActionID > 0)
                    //{
                    //    jr = new JTokenReader(jObj["PAssignTo"]);
                    //    jr.Read();
                    //    PAssignedTo = Convert.ToInt32(jr.Value);

                    //    jr = new JTokenReader(jObj["AddresseType"]);
                    //    jr.Read();
                    //    AddresseType = Convert.ToByte(jr.Value);

                    //    jr = new JTokenReader(jObj["PActionRemarks"]);
                    //    jr.Read();
                    //    PActionRemarks = Common.DecryptString(jr.Value.ToString());
                    //}

                    //var PNextStepResult = API.GetNextStepType(CompanyID, WorkFlowID, CurrentStepID, PActionID);
                    //JObject PjObject = JObject.Parse(NextStepResult.Data.ToString());
                    //jr = new JTokenReader(jObject["NextStepID"]);
                    //jr.Read();
                    //PNextStepID = Convert.ToInt32(jr.Value);

                    //jr = new JTokenReader(jObject["NextStepType"]);
                    //jr.Read();
                    //PNextStapName = jr.Value.ToString();

                    //jr = new JTokenReader(jObject["RoleID"]);
                    //jr.Read();
                    //PRoleID = Convert.ToInt32(jr.Value);

                    //jr = new JTokenReader(jObject["ToStepStatus"]);
                    //jr.Read();
                    //PCallStatus_ID = Convert.ToInt32(jr.Value);

                    //hSR.CallStatus_ID = CallStatus_ID;
                    //hSR.ActionRemarks = PActionRemarks;

                    //-----------------------------Parent Work Flow Details Save END --------------------------------------------------------

                    //PhSR.ServiceRequestNumber = "";
                    //PhSR.ServiceRequestDate = Common.LocalTime(BranchID, DateTime.Now);
                    //PhSR.CallStatus_ID = 0;
                    //PhSR.ParentIssue_ID = hSR.ParentIssue_ID == 0 ? null : hSR.ParentIssue_ID;

                    //PhSR.Product_Unique_Number = Common.DecryptString(hSR.Product_Unique_Number);
                    //PhSR.Party_ID = hSR.Party_ID;
                    //PhSR.PartyContactPerson_ID = hSR.PartyContactPerson_ID;

                    //PhSR.Model_ID = (hSR.Model_ID == 0) ? null : hSR.Model_ID;
                    //PhSR.Brand_ID = (hSR.Brand_ID == 0) ? null : hSR.Brand_ID;
                    //PhSR.ProductType_ID = (hSR.ProductType_ID == 0) ? null : hSR.ProductType_ID;
                    //PhSR.SerialNumber = hSR.SerialNumber;
                    //PhSR.Product_ID = HelpDeskControllerObj.getProductID(hSR.SerialNumber, hSR.Model_ID);
                    //PhSR.ProductReading = (hSR.ProductReading == 0) ? null : hSR.ProductReading;
                    //PhSR.IsUnderWarranty = hSR.IsUnderWarranty;

                    //PhSR.Region_ID = (hSR.Region_ID == 0) ? null : hSR.Region_ID;
                    //PhSR.Company_ID = CompanyID;
                    //PhSR.Branch_ID = BranchID;
                    //PhSR.ModifiedDate = Common.LocalTime(BranchID, DateTime.Now);
                    //PhSR.ModifiedBy_ID = UserID;
                    //PhSR.CallOwner_ID = (PhSR.CallOwner_ID) == 0 ? null : PhSR.CallOwner_ID;
                    //PhSR.Document_no = null;

                    //hSR.ParentIssue_ID = PSRID;

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        string query = @"
                            INSERT INTO HD_ServiceRequest (
                                ServiceRequestNumber, ServiceRequestDate, Quotation_ID, QuotationNumber, 
                                JobCard_ID, JobCardNumber, CallStatus_ID, ParentIssue_ID, Product_Unique_Number, 
                                Party_ID, PartyContactPerson_ID, Model_ID, Brand_ID, ProductType_ID, 
                                SerialNumber, ProductReading, IsUnderWarranty, CallMode_ID, CallPriority_ID, 
                                CallComplexity_ID, CallDateAndTime, PromisedCompletionDate, Region_ID, 
                                CallDescription, IssueArea_ID, IssueSubArea_ID, FunctionGroup_ID, IsUnderBreakDown, 
                                QuestionaryLevel1_ID, QuestionaryLevel2_ID, QuestionaryLevel3_ID, DefectGroup_ID, 
                                DefectName_ID, RootCause, InformationCollected, CallClosureDateAndTime, ClosureType_ID, 
                                ClosingDescription, Company_ID, Branch_ID, ModifiedDate, ModifiedBy_ID, Document_no, 
                                CaseType_ID, ActionRemarks, Product_ID, CustomerRating, FinancialYear, IsCallBlocked, 
                                StockBlocking_ID, EnquiryStage_ID, SalesQuotation_ID, SalesQuotationNumber, 
                                SalesOrder_ID, SalesOrderNumber, SalesOrderDate, CallOwner_ID, Flexi1, Flexi2, 
                                ResolutionTime, ResponseTime, AttachmentCount, CustomerFeedbackDate, IsNegetiveFeedback, 
                                IsDealer, IsDealerList, ProductRateType, ChildTicket_Sequence_ID, ResponseTime24, 
                                ResolutionTime24, Current_AssignTo, ContractorID, ContractorContactPerson_ID, 
                                ScheduledType_ID, ExpectedArrivalDateTime, ActualArrivalDateTime, 
                                ExpectedDepartureDateTime, ActualDepartureDateTime, NoofTechs, ShiftHours, IsWIPBay
                            ) 
                            VALUES (
                                @ServiceRequestNumber, @ServiceRequestDate, @Quotation_ID, @QuotationNumber, 
                                @JobCard_ID, @JobCardNumber, @CallStatus_ID, @ParentIssue_ID, @Product_Unique_Number, 
                                @Party_ID, @PartyContactPerson_ID, @Model_ID, @Brand_ID, @ProductType_ID, 
                                @SerialNumber, @ProductReading, @IsUnderWarranty, @CallMode_ID, @CallPriority_ID, 
                                @CallComplexity_ID, @CallDateAndTime, @PromisedCompletionDate, @Region_ID, 
                                @CallDescription, @IssueArea_ID, @IssueSubArea_ID, @FunctionGroup_ID, @IsUnderBreakDown, 
                                @QuestionaryLevel1_ID, @QuestionaryLevel2_ID, @QuestionaryLevel3_ID, @DefectGroup_ID, 
                                @DefectName_ID, @RootCause, @InformationCollected, @CallClosureDateAndTime, @ClosureType_ID, 
                                @ClosingDescription, @Company_ID, @Branch_ID, @ModifiedDate, @ModifiedBy_ID, @Document_no, 
                                @CaseType_ID, @ActionRemarks, @Product_ID, @CustomerRating, @FinancialYear, @IsCallBlocked, 
                                @StockBlocking_ID, @EnquiryStage_ID, @SalesQuotation_ID, @SalesQuotationNumber, 
                                @SalesOrder_ID, @SalesOrderNumber, @SalesOrderDate, @CallOwner_ID, @Flexi1, @Flexi2, 
                                @ResolutionTime, @ResponseTime, @AttachmentCount, @CustomerFeedbackDate, @IsNegetiveFeedback, 
                                @IsDealer, @IsDealerList, @ProductRateType, @ChildTicket_Sequence_ID, @ResponseTime24, 
                                @ResolutionTime24, @Current_AssignTo, @ContractorID, @ContractorContactPerson_ID, 
                                @ScheduledType_ID, @ExpectedArrivalDateTime, @ActualArrivalDateTime, 
                                @ExpectedDepartureDateTime, @ActualDepartureDateTime, @NoofTechs, @ShiftHours, @IsWIPBay
                            );
                        ";

                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            // Add parameters
                            command.Parameters.AddWithValue("@ServiceRequestNumber", hSR.ServiceRequestNumber ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ServiceRequestDate", hSR.ServiceRequestDate);
                            command.Parameters.AddWithValue("@Quotation_ID", hSR.Quotation_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@QuotationNumber", hSR.QuotationNumber ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@JobCard_ID", hSR.JobCard_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@JobCardNumber", hSR.JobCardNumber ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CallStatus_ID", hSR.CallStatus_ID);
                            command.Parameters.AddWithValue("@ParentIssue_ID", hSR.ParentIssue_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Product_Unique_Number", hSR.Product_Unique_Number ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Party_ID", hSR.Party_ID);
                            command.Parameters.AddWithValue("@PartyContactPerson_ID", hSR.PartyContactPerson_ID);
                            command.Parameters.AddWithValue("@Model_ID", hSR.Model_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Brand_ID", hSR.Brand_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ProductType_ID", hSR.ProductType_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SerialNumber", hSR.SerialNumber ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ProductReading", hSR.ProductReading ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsUnderWarranty", hSR.IsUnderWarranty);
                            command.Parameters.AddWithValue("@CallMode_ID", hSR.CallMode_ID);
                            command.Parameters.AddWithValue("@CallPriority_ID", hSR.CallPriority_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CallComplexity_ID", hSR.CallComplexity_ID);
                            command.Parameters.AddWithValue("@CallDateAndTime", hSR.CallDateAndTime);
                            command.Parameters.AddWithValue("@PromisedCompletionDate", hSR.PromisedCompletionDate ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Region_ID", hSR.Region_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CallDescription", hSR.CallDescription ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IssueArea_ID", hSR.IssueArea_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IssueSubArea_ID", hSR.IssueSubArea_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@FunctionGroup_ID", hSR.FunctionGroup_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsUnderBreakDown", hSR.IsUnderBreakDown);
                            command.Parameters.AddWithValue("@QuestionaryLevel1_ID", hSR.QuestionaryLevel1_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@QuestionaryLevel2_ID", hSR.QuestionaryLevel2_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@QuestionaryLevel3_ID", hSR.QuestionaryLevel3_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@DefectGroup_ID", hSR.DefectGroup_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@DefectName_ID", hSR.DefectName_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@RootCause", hSR.RootCause ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@InformationCollected", hSR.InformationCollected ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CallClosureDateAndTime", hSR.CallClosureDateAndTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ClosureType_ID", hSR.ClosureType_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ClosingDescription", hSR.ClosingDescription ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Company_ID", hSR.Company_ID);
                            command.Parameters.AddWithValue("@Branch_ID", hSR.Branch_ID);
                            command.Parameters.AddWithValue("@ModifiedDate", hSR.ModifiedDate ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ModifiedBy_ID", hSR.ModifiedBy_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Document_no", hSR.Document_no ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CaseType_ID", hSR.CaseType_ID);
                            command.Parameters.AddWithValue("@ActionRemarks", hSR.ActionRemarks ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Product_ID", hSR.Product_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CustomerRating", hSR.CustomerRating ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@FinancialYear", hSR.FinancialYear ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsCallBlocked", hSR.IsCallBlocked ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@StockBlocking_ID", hSR.StockBlocking_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@EnquiryStage_ID", hSR.EnquiryStage_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SalesQuotation_ID", hSR.SalesQuotation_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SalesQuotationNumber", hSR.SalesQuotationNumber ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SalesOrder_ID", hSR.SalesOrder_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SalesOrderNumber", hSR.SalesOrderNumber ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SalesOrderDate", hSR.SalesOrderDate ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CallOwner_ID", hSR.CallOwner_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Flexi1", hSR.Flexi1 ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Flexi2", hSR.Flexi2 ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ResolutionTime", hSR.ResolutionTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ResponseTime", hSR.ResponseTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@AttachmentCount", hSR.AttachmentCount ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@CustomerFeedbackDate", hSR.CustomerFeedbackDate ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsNegetiveFeedback", hSR.IsNegetiveFeedback ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsDealer", hSR.IsDealer ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsDealerList", hSR.IsDealerList ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ProductRateType", hSR.ProductRateType ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ChildTicket_Sequence_ID", hSR.ChildTicket_Sequence_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ResponseTime24", hSR.ResponseTime24 ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ResolutionTime24", hSR.ResolutionTime24 ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Current_AssignTo", hSR.Current_AssignTo ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ContractorID", hSR.ContractorID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ContractorContactPerson_ID", hSR.ContractorContactPerson_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ScheduledType_ID", hSR.ScheduledType_ID ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ExpectedArrivalDateTime", hSR.ExpectedArrivalDateTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ActualArrivalDateTime", hSR.ActualArrivalDateTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ExpectedDepartureDateTime", hSR.ExpectedDepartureDateTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ActualDepartureDateTime", hSR.ActualDepartureDateTime ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@NoofTechs", hSR.NoofTechs ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ShiftHours", hSR.ShiftHours ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsWIPBay", hSR.IsWIPBay ?? (object)DBNull.Value);

                            // Open the connection and execute the query
                            connection.Open();
                            command.ExecuteNonQuery();
                        }
                    }

                    string PServicereqNumber = hSR.ServiceRequestNumber;
                    int? PSRID = 0;
                    int? ChildTicket_Sequence_ID = 0;
                    PSRID = hSR.ParentIssue_ID;
                    ChildTicket_Sequence_ID = hSR.ChildTicket_Sequence_ID;


                    //hSR.ParentIssue_ID = PSRID;
                    //hSR.ResponseTime = CFC.ConvertToHours(CFC.getWorkingHoursForRTme(Common.LocalTime(BranchID, Convert.ToDateTime(hSR.CallDateAndTime)), Common.LocalTime(BranchID, DateTime.Now), hSR.Company_ID, isFromWebAPI));
                    //hSRClient.HD_ServiceRequest.Add(hSR);
                    //hSRClient.SaveChanges();
                    //string ServicereqNumber = hSR.ServiceRequestNumber;
                    if (Convert.ToString(hSR.ProductReading) != "")
                    {
                        if (hSR.ProductReading != 0)
                        {

                            using (var conn = new SqlConnection(connString))
                            {
                                conn.Open();

                                string query = "SELECT * FROM HD_ServiceRequest";

                                using (var cmd = new SqlCommand(query, conn))
                                {
                                    using (var reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            var refMasterDetailObj = new HD_ServiceRequest
                                            {
                                                ServiceRequest_ID = reader["ServiceRequest_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ServiceRequest_ID"]),
                                                ServiceRequestNumber = reader["ServiceRequestNumber"] == DBNull.Value ? string.Empty : reader["ServiceRequestNumber"].ToString(),
                                                ServiceRequestDate = reader["ServiceRequestDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["ServiceRequestDate"]),
                                                Quotation_ID = reader["Quotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Quotation_ID"]),
                                                QuotationNumber = reader["QuotationNumber"] == DBNull.Value ? string.Empty : reader["QuotationNumber"].ToString(),
                                                JobCard_ID = reader["JobCard_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["JobCard_ID"]),
                                                JobCardNumber = reader["JobCardNumber"] == DBNull.Value ? string.Empty : reader["JobCardNumber"].ToString(),
                                                CallStatus_ID = reader["CallStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallStatus_ID"]),
                                                ParentIssue_ID = reader["ParentIssue_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ParentIssue_ID"]),
                                                Product_Unique_Number = reader["Product_Unique_Number"] == DBNull.Value ? string.Empty : reader["Product_Unique_Number"].ToString(),
                                                Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                                PartyContactPerson_ID = reader["PartyContactPerson_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                                Model_ID = reader["Model_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Model_ID"]),
                                                Brand_ID = reader["Brand_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Brand_ID"]),
                                                ProductType_ID = reader["ProductType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductType_ID"]),
                                                SerialNumber = reader["SerialNumber"] == DBNull.Value ? string.Empty : reader["SerialNumber"].ToString(),
                                                ProductReading = reader["ProductReading"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductReading"]),
                                                IsUnderWarranty = reader["IsUnderWarranty"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderWarranty"]),
                                                CallMode_ID = reader["CallMode_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallMode_ID"]),
                                                CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallPriority_ID"]),
                                                CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                                CallDateAndTime = reader["CallDateAndTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["CallDateAndTime"]),
                                                PromisedCompletionDate = reader["PromisedCompletionDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["PromisedCompletionDate"]),
                                                Region_ID = reader["Region_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Region_ID"]),
                                                CallDescription = reader["CallDescription"] == DBNull.Value ? string.Empty : reader["CallDescription"].ToString(),
                                                IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueArea_ID"]),
                                                IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                                FunctionGroup_ID = reader["FunctionGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FunctionGroup_ID"]),
                                                IsUnderBreakDown = reader["IsUnderBreakDown"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderBreakDown"]),
                                                QuestionaryLevel1_ID = reader["QuestionaryLevel1_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel1_ID"]),
                                                QuestionaryLevel2_ID = reader["QuestionaryLevel2_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel2_ID"]),
                                                QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel3_ID"]),
                                                DefectGroup_ID = reader["DefectGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectGroup_ID"]),
                                                DefectName_ID = reader["DefectName_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectName_ID"]),
                                                RootCause = reader["RootCause"] == DBNull.Value ? string.Empty : reader["RootCause"].ToString(),
                                                InformationCollected = reader["InformationCollected"] == DBNull.Value ? string.Empty : reader["InformationCollected"].ToString(),
                                                CallClosureDateAndTime = reader["CallClosureDateAndTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CallClosureDateAndTime"]),
                                                ClosureType_ID = reader["ClosureType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ClosureType_ID"]),
                                                ClosingDescription = reader["ClosingDescription"] == DBNull.Value ? string.Empty : reader["ClosingDescription"].ToString(),
                                                Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                                Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                                ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ModifiedDate"]),
                                                ExpectedArrivalDateTime = reader["ExpectedArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedArrivalDateTime"]),
                                                ActualArrivalDateTime = reader["ActualArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualArrivalDateTime"]),
                                                ExpectedDepartureDateTime = reader["ExpectedDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedDepartureDateTime"]),
                                                ActualDepartureDateTime = reader["ActualDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualDepartureDateTime"]),
                                                ModifiedBy_ID = reader["ModifiedBy_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ModifiedBy_ID"]),
                                                Document_no = reader["Document_no"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Document_no"]),
                                                CaseType_ID = reader["CaseType_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CaseType_ID"]),
                                                ActionRemarks = reader["ActionRemarks"] == DBNull.Value ? string.Empty : reader["ActionRemarks"].ToString(),
                                                Product_ID = reader["Product_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Product_ID"]),
                                                CustomerRating = reader["CustomerRating"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CustomerRating"]),
                                                FinancialYear = reader["FinancialYear"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FinancialYear"]),
                                                IsCallBlocked = reader["IsCallBlocked"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsCallBlocked"]),
                                                StockBlocking_ID = reader["StockBlocking_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["StockBlocking_ID"]),
                                                EnquiryStage_ID = reader["EnquiryStage_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["EnquiryStage_ID"]),
                                                SalesQuotation_ID = reader["SalesQuotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesQuotation_ID"]),
                                                SalesQuotationNumber = reader["SalesQuotationNumber"] == DBNull.Value ? string.Empty : reader["SalesQuotationNumber"].ToString(),
                                                SalesOrder_ID = reader["SalesOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesOrder_ID"]),
                                                SalesOrderNumber = reader["SalesOrderNumber"] == DBNull.Value ? string.Empty : reader["SalesOrderNumber"].ToString(),
                                                SalesOrderDate = reader["SalesOrderDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["SalesOrderDate"]),
                                                CallOwner_ID = reader["CallOwner_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallOwner_ID"]),
                                                Current_AssignTo = reader["Current_AssignTo"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Current_AssignTo"]),
                                                ContractorID = reader["ContractorID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorID"]),
                                                ContractorContactPerson_ID = reader["ContractorContactPerson_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorContactPerson_ID"]),
                                                ScheduledType_ID = reader["ScheduledType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ScheduledType_ID"]),
                                                NoofTechs = reader["NoofTechs"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["NoofTechs"]),
                                                ShiftHours = reader["ShiftHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ShiftHours"]),
                                                Flexi1 = reader["Flexi1"] == DBNull.Value ? string.Empty : reader["Flexi1"].ToString(),
                                                Flexi2 = reader["Flexi2"] == DBNull.Value ? string.Empty : reader["Flexi2"].ToString(),
                                                ResolutionTime = reader["ResolutionTime"] == DBNull.Value ? string.Empty : reader["ResolutionTime"].ToString(),
                                                ResponseTime = reader["ResponseTime"] == DBNull.Value ? string.Empty : reader["ResponseTime"].ToString(),
                                                AttachmentCount = reader["AttachmentCount"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["AttachmentCount"]),
                                                CustomerFeedbackDate = reader["CustomerFeedbackDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CustomerFeedbackDate"]),
                                                IsNegetiveFeedback = reader["IsNegetiveFeedback"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsNegetiveFeedback"]),
                                                IsDealer = reader["IsDealer"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsDealer"]),
                                                IsWIPBay = reader["IsWIPBay"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsWIPBay"]),
                                                IsDealerList = reader["IsDealerList"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["IsDealerList"]),
                                                ProductRateType = reader["ProductRateType"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["ProductRateType"]),
                                                ChildTicket_Sequence_ID = reader["ChildTicket_Sequence_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ChildTicket_Sequence_ID"]),
                                                ResponseTime24 = reader["ResponseTime24"] == DBNull.Value ? string.Empty : reader["ResponseTime24"].ToString(),
                                                ResolutionTime24 = reader["ResolutionTime24"] == DBNull.Value ? string.Empty : reader["ResolutionTime24"].ToString(),
                                            };

                                            ServiceRequestList.Add(refMasterDetailObj);
                                        }
                                    }
                                }
                            }

                            List<GNM_ProductReading> ProductReadingList = new List<GNM_ProductReading>();

                            using (var conn = new SqlConnection(connString))
                            {
                                conn.Open();

                                string query = "SELECT * FROM GNM_ProductReading";

                                using (var cmd = new SqlCommand(query, conn))
                                {
                                    using (var reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            var refMasterDetailObj = new GNM_ProductReading
                                            {
                                                Product_ID = reader["Product_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Product_ID"]),
                                                ProductReadingID = reader["ProductReadingID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ProductReadingID"]),
                                                Reading = reader["Reading"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Reading"]),
                                            };

                                            ProductReadingList.Add(refMasterDetailObj);
                                        }
                                    }
                                }
                            }
                            string ServiceRequestNumberR = ServiceRequestList.Where(sr => sr.ServiceRequest_ID == hSR.ServiceRequest_ID).Select(i => i.ServiceRequestNumber).FirstOrDefault();
                            int CurrentReading = ProductReadingList.Where(i => i.Product_ID == hSR.Product_ID).OrderByDescending(i => i.ProductReadingID).Select(i => i.Reading).FirstOrDefault() == 0 ? 0 : ProductReadingList.Where(i => i.Product_ID == hSR.Product_ID).OrderByDescending(i => i.ProductReadingID).Select(i => i.Reading).FirstOrDefault();
                            bool IsValidReading = (hSR.ProductReading > CurrentReading) ? true : false;
                            if (IsValidReading)
                            {
                                GNM_ProductReading NewRow = new GNM_ProductReading();
                                NewRow.Product_ID = hSR.Product_ID;
                                NewRow.Mode = 3;
                                NewRow.Company_ID = Convert.ToInt32(hSR.Company_ID);
                                NewRow.Reference_Number = ServiceRequestNumberR;
                                NewRow.JobCardId = null;
                                NewRow.Reference_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                NewRow.Reading = Convert.ToInt32(hSR.ProductReading);
                                using (SqlConnection connection = new SqlConnection(connString))
                                {
                                    connection.Open();

                                    string insertQuery = @"
                                                        INSERT INTO GNM_ProductReading (
                                                            Product_ID, 
                                                            ProductComponent_ID, 
                                                            Mode, 
                                                            Company_ID, 
                                                            Reference_Number, 
                                                            Reference_Date, 
                                                            Reading, 
                                                            JobCardId, 
                                                            AccumulatedHMR
                                                        ) 
                                                        VALUES (
                                                            @Product_ID, 
                                                            @ProductComponent_ID, 
                                                            @Mode, 
                                                            @Company_ID, 
                                                            @Reference_Number, 
                                                            @Reference_Date, 
                                                            @Reading, 
                                                            @JobCardId, 
                                                            @AccumulatedHMR
                                                        );
                                                        SELECT SCOPE_IDENTITY();";

                                    using (SqlCommand command = new SqlCommand(insertQuery, connection))
                                    {
                                        // Add parameters
                                        command.Parameters.AddWithValue("@Product_ID", NewRow.Product_ID ?? (object)DBNull.Value);
                                        command.Parameters.AddWithValue("@ProductComponent_ID", NewRow.ProductComponent_ID ?? (object)DBNull.Value);
                                        command.Parameters.AddWithValue("@Mode", NewRow.Mode);
                                        command.Parameters.AddWithValue("@Company_ID", NewRow.Company_ID);
                                        command.Parameters.AddWithValue("@Reference_Number", NewRow.Reference_Number ?? (object)DBNull.Value);
                                        command.Parameters.AddWithValue("@Reference_Date", NewRow.Reference_Date);
                                        command.Parameters.AddWithValue("@Reading", NewRow.Reading);
                                        command.Parameters.AddWithValue("@JobCardId", NewRow.JobCardId ?? (object)DBNull.Value);
                                        command.Parameters.AddWithValue("@AccumulatedHMR", NewRow.AccumulatedHMR ?? (object)DBNull.Value);

                                        // Execute the query and get the inserted ID
                                        object result = command.ExecuteScalar();
                                        if (result != null)
                                        {
                                            NewRow.ProductReadingID = Convert.ToInt32(result);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (ContactPersonObj != "" && (hSR.IsDealer == false || hSR.IsDealer == null))
                    {
                        jobcp = JObject.Parse(ContactPersonObj);
                        GNM_PartyContactPersonDetails contactPersonRow = jobcp.ToObject<GNM_PartyContactPersonDetails>();

                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM GNM_PartyContactPersonDetails";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new GNM_PartyContactPersonDetails
                                        {
                                            PartyContactPerson_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        };

                                        PartyContactPersonDetailsList.Add(refMasterDetailObj);
                                    }
                                }
                            }

                            partyContact = PartyContactPersonDetailsList.Where(i => i.PartyContactPerson_ID == contactPersonRow.PartyContactPerson_ID).FirstOrDefault();
                            if (partyContact.PartyContactPerson_ID != 0)
                            {
                                partyContact.PartyContactPerson_Mobile = await _utilityServiceClient.DecryptStringAsync(contactPersonRow.PartyContactPerson_Mobile);
                                partyContact.PartyContactPerson_Email = await _utilityServiceClient.DecryptStringAsync(contactPersonRow.PartyContactPerson_Email);
                            }
                            if (partyContact != null && partyContact.PartyContactPerson_ID != 0)
                            {
                                // Update the contact person's details
                                string updateQuery = @"
                                                UPDATE GNM_PartyContactPersonDetails
                                                SET 
                                                    Party_ID = @Party_ID,
                                                    PartyContactPerson_Name = @PartyContactPerson_Name,
                                                    PartyContactPerson_Email = @PartyContactPerson_Email,
                                                    PartyContactPerson_Department = @PartyContactPerson_Department,
                                                    PartyContactPerson_Mobile = @PartyContactPerson_Mobile,
                                                    PartyContactPerson_Phone = @PartyContactPerson_Phone,
                                                    Party_IsDefaultContact = @Party_IsDefaultContact,
                                                    PartyContactPerson_IsActive = @PartyContactPerson_IsActive,
                                                    PartyContactPerson_Remarks = @PartyContactPerson_Remarks,
                                                    Language_ID = @Language_ID,
                                                    PartyContactPerson_DOB = @PartyContactPerson_DOB
                                                WHERE 
                                                    PartyContactPerson_ID = @PartyContactPerson_ID";

                                using (var updateCmd = new SqlCommand(updateQuery, conn))
                                {
                                    updateCmd.Parameters.AddWithValue("@Party_ID", partyContact.Party_ID ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_Name", partyContact.PartyContactPerson_Name ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_Email", await _utilityServiceClient.DecryptStringAsync(contactPersonRow.PartyContactPerson_Email) ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_Department", partyContact.PartyContactPerson_Department ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_Mobile", await _utilityServiceClient.DecryptStringAsync(contactPersonRow.PartyContactPerson_Mobile) ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_Phone", partyContact.PartyContactPerson_Phone ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@Party_IsDefaultContact", partyContact.Party_IsDefaultContact);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_IsActive", partyContact.PartyContactPerson_IsActive);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_Remarks", partyContact.PartyContactPerson_Remarks ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@Language_ID", partyContact.Language_ID);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_DOB", partyContact.PartyContactPerson_DOB ?? (object)DBNull.Value);
                                    updateCmd.Parameters.AddWithValue("@PartyContactPerson_ID", contactPersonRow.PartyContactPerson_ID);
                                    updateCmd.ExecuteNonQuery();
                                }
                            }
                        }
                    }

                    if (Reopen != "" && hSR.ParentIssue_ID != null)
                    {
                        //HelpDeskControllerObj.InsertAttachments_PartLists_Notes(hSR.ParentIssue_ID, hSR.ServiceRequest_ID);
                    }
                    if (hSR.ExpectedArrivalDateTime != null && hSR.ExpectedDepartureDateTime != null && hSR.ParentIssue_ID == null)
                    {
                        using (SqlConnection SQLConn = new SqlConnection(connString))
                        {
                            if (SQLConn.State == ConnectionState.Closed || SQLConn.State == ConnectionState.Broken)
                            {
                                SQLConn.Open();
                                SqlCommand SQLCmd = new SqlCommand("Up_UpdateWorkshopBookingDetails", SQLConn);
                                SQLCmd.CommandType = CommandType.StoredProcedure;
                                SQLCmd.Parameters.AddWithValue("@Branch_ID", hSR.Branch_ID);
                                SQLCmd.Parameters.AddWithValue("@ExpectedArrivalDate", hSR.ExpectedArrivalDateTime);
                                SQLCmd.Parameters.AddWithValue("@ExpectedDepartureDate", hSR.ExpectedDepartureDateTime);
                                SQLCmd.Parameters.AddWithValue("@IsWIPBay", hSR.IsWIPBay);
                                SQLCmd.Parameters.AddWithValue("@ServiceRequest_ID", hSR.ServiceRequest_ID);
                                SQLCmd.Parameters.AddWithValue("@Quotation_ID", hSR.Quotation_ID == null ? 0 : hSR.Quotation_ID);
                                SQLCmd.Parameters.AddWithValue("@ShiftHours", hSR.ShiftHours);
                                SQLCmd.Parameters.AddWithValue("@NoOfTechs", hSR.NoofTechs);
                                SQLCmd.Parameters.AddWithValue("@IsQuoteAddMode", 0);
                                SQLCmd.Parameters.AddWithValue("@ModifiedBy", UserID);
                                SQLCmd.Parameters.AddWithValue("@ModifiedDate", await _utilityServiceClient.LocalTimeAsync(hSR.Branch_ID, DateTime.Now, connString));
                                SQLCmd.ExecuteNonQuery();
                            }
                            SQLConn.Close();
                        }
                    }

                    int TransactionID = hSR.ServiceRequest_ID;

                    GNM_ProductCustomer customerupdate = new GNM_ProductCustomer();
                    // CoreProductMasterServices.GNM_Branch branchdata = new CoreProductMasterServices.GNM_Branch();
                    GNM_Branch branchdata = new GNM_Branch();
                    if (hSR.IsDealer == true)
                    {


                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM GNM_Branch";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        // var refMasterDetailObj = new CoreProductMasterServices.GNM_Branch
                                        var refMasterDetailObj = new GNM_Branch
                                        {
                                            Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                            Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                            Branch_Name = reader["Branch_Name"] == DBNull.Value ? null : reader["Branch_Name"].ToString(),
                                        };

                                        BranchList.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }
                        branchdata = BranchList.Where(a => a.Branch_ID == hSR.Party_ID).FirstOrDefault();
                        CustomerMobNo = branchdata.Branch_Mobile;
                        CustomerEmailID = branchdata.Branch_Email;

                    }
                    else
                    {
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM GNM_PartyContactPersonDetails";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new GNM_PartyContactPersonDetails
                                        {
                                            PartyContactPerson_ID = reader["PartyContactPerson_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                        };

                                        PartyContactPersonDetailsList.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }

                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM GNM_ProductCustomer";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new GNM_ProductCustomer
                                        {
                                            Product_ID = reader["Product_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Product_ID"]),
                                            ProductCustomer_ID = reader["ProductCustomer_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ProductCustomer_ID"]),
                                        };

                                        ProductCustomerList.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }
                        partyContact = PartyContactPersonDetailsList.Where(p => p.PartyContactPerson_ID == hSR.PartyContactPerson_ID).FirstOrDefault();
                        CustomerMobNo = partyContact.PartyContactPerson_Mobile;
                        CustomerEmailID = partyContact.PartyContactPerson_Email;
                        customerupdate = ProductCustomerList.Where(a => a.Product_ID == hSR.Product_ID && a.ProductCustomer_ID == ProductCustomerid).FirstOrDefault();
                    }
                    if (customerupdate != null)
                    {
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();
                            string updateQuery = @"
                                                UPDATE GNM_ProductCustomer
                                                SET IsTransacted = @IsTransacted
                                                WHERE Product_ID = @Product_ID AND ProductCustomer_ID = @ProductCustomer_ID";

                            using (var updateCmd = new SqlCommand(updateQuery, conn))
                            {
                                updateCmd.Parameters.AddWithValue("@IsTransacted", true);
                                updateCmd.Parameters.AddWithValue("@Product_ID", customerupdate.Product_ID);
                                updateCmd.Parameters.AddWithValue("@ProductCustomer_ID", customerupdate.ProductCustomer_ID);
                                updateCmd.ExecuteNonQuery();
                            }
                        }
                    }
                    if (hSR.IsDealer == true && branchdata.Branch_Mobile != "")
                    {
                        SMStoAssigneeText = getSMSTextAssignee(TransactionID, Convert.ToBoolean(hSR.IsDealer), connString);
                    }
                    else if (hSR.IsDealer == false || hSR.IsDealer == null)
                    {
                        SMStoAssigneeText = getSMSTextAssignee(TransactionID, Convert.ToBoolean(hSR.IsDealer), connString);

                    }

                    SMStoCustomerText = getSMSTextCustomer(TransactionID, AssignedTo, AddresseType, connString, LogException, HelpLineNumber);
                    HD_ServiceRequest ServiceRequestObj = ServiceRequestList.Where(sr => sr.ServiceRequest_ID == TransactionID).FirstOrDefault();
                    string ServiceRequestNumber = ServiceRequestList.Where(sr => sr.ServiceRequest_ID == TransactionID).Select(i => i.ServiceRequestNumber).FirstOrDefault();
                    //EmailSub = "Enquiry #:" + ServiceRequestNumber;
                    //gbl.InsertGPSDetails(CompanyID, BranchID, UserID, Convert.ToInt32(Common.GetObjectID("HelpDeskUserLandingPage")), hSR.ServiceRequest_ID, 0, 0, "Inserted" + " " + ServiceRequestNumber, false, Convert.ToInt32(RequestParams[13]), Loggindate, null);
                    StringBuilder[] templateDetails = getEmailBodyAssignee(connString, LogException, TransactionID, UserLanguageCode, Convert.ToBoolean(hSR.IsDealer), ReLoginView, Language_ID, UserCulture);
                    EmailSub = templateDetails[0].ToString();
                    //added by Kavitha-start
                    AddresseCC = templateDetails[4].ToString();
                    AddresseEmailBCC = templateDetails[3].ToString();
                    //added by Kavitha-end
                    EmailtoAssigneeBody = templateDetails[1].ToString();
                    StringBuilder[] ctemplateDetails = getEmailBodyCustomer(TransactionID, AssignedTo, AddresseType, IsSendPCDMail, UserLanguageCode, Language_ID, Employee_ID, connString, LogException);
                    EmailtoCustomerBody = ctemplateDetails[1].ToString();
                    //added by Kavitha-start
                    CustBCC = ctemplateDetails[3].ToString();
                    CustCC = ctemplateDetails[4].ToString();
                    //added by Kavitha-end
                    GNM_PartyContactPersonDetails PartyContactPerson = new GNM_PartyContactPersonDetails();
                    string ContactPersonEmail = string.Empty;
                    string ContactPersonMobile = string.Empty;
                    if (hSR.IsDealer == true)
                    {
                        branchdata = BranchList.Where(a => a.Branch_ID == hSR.Party_ID).FirstOrDefault();
                        ContactPersonMobile = branchdata.Branch_Mobile;
                        ContactPersonEmail = branchdata.Branch_Email;
                    }
                    else { PartyContactPerson = PartyContactPersonDetailsList.Where(p => p.PartyContactPerson_ID == hSR.PartyContactPerson_ID).FirstOrDefault(); }


                    using (var conn = new SqlConnection(connString))
                    {
                        conn.Open();

                        string query = "SELECT * FROM GNM_CompParam";

                        using (var cmd = new SqlCommand(query, conn))
                        {
                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var refMasterDetailObj = new GNM_CompParam
                                    {
                                        Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                        Param_Name = reader["Param_Name"] == DBNull.Value ? null : reader["Param_Name"].ToString(),
                                        Param_value = reader["Param_value"] == DBNull.Value ? null : reader["Param_value"].ToString(),
                                    };

                                    CompParamList.Add(refMasterDetailObj);
                                }
                            }
                        }
                    }
                    bool RegistrationEmailandSMS = Convert.ToBoolean(CompParamList.Where(cp => cp.Param_Name.ToUpper() == "REGISTRATIONEMAILANDSMS" && cp.Company_ID == CompanyID).Select(cp => cp.Param_value).FirstOrDefault());
                    if (RegistrationEmailandSMS)
                    {
                        InsertCustomerComplaintRegistationEmailandSMS(ServiceRequestObj, PartyContactPerson, ContactPersonEmail, Convert.ToBoolean(hSR.IsDealer), ContactPersonMobile, EmailSub, ServiceRequestNumber, UserLanguageCode, Company_ID, Employee_ID, connString, LogException);//Send Email to Customer after complaint is registered.
                    }

                    WF_WFStepLink stepLinkRow = GetRoleOrIndividualForFirstStep(CompanyID, WorkFlowID, connString);
                    byte firstRoleorIndividual = stepLinkRow != null ? stepLinkRow.Addresse_Flag : Convert.ToByte(1);
                    int roleID = stepLinkRow != null ? (stepLinkRow.Addresse_Flag == 0 ? Convert.ToInt32(stepLinkRow.Addresse_WFRole_ID) : User_ID) : User_ID;

                    CaseProgressObjects CPDetails = new CaseProgressObjects();
                    CPDetails.actionBy = Convert.ToInt32(UserID);
                    CPDetails.actionID = ActionID;
                    CPDetails.actionRemarks = ActionRemarks;
                    CPDetails.actionTime = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                    CPDetails.addresseType = firstRoleorIndividual;
                    CPDetails.AssignTo = roleID;
                    CPDetails.CompanyID = Convert.ToInt32(CompanyID);
                    CPDetails.currentStepID = CurrentStepID;
                    CPDetails.customerEmailID = CustomerEmailID;
                    CPDetails.customerMobileNumber = CustomerMobNo;
                    CPDetails.emailBodyAddress = EmailtoAssigneeBody.Replace("\n", "<br/>").ToString();
                    CPDetails.emailBodyCustomer = EmailtoCustomerBody.Replace("\n", "<br/>").ToString();
                    CPDetails.emailSubAddressee = EmailSub;
                    CPDetails.emailSubCustomer = EmailSub;
                    CPDetails.NextStepID = CurrentStepID;
                    CPDetails.receivedTime = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                    CPDetails.RoleID = RoleID;
                    CPDetails.smsTextAddressee = SMStoAssigneeText;
                    CPDetails.smsTextCustomer = SMStoCustomerText;
                    CPDetails.transactionNumber = hSR.ServiceRequest_ID;
                    //added by Kavitha-start
                    CPDetails.AddresseBcc = AddresseEmailBCC;
                    CPDetails.AddresseCC = AddresseCC;
                    CPDetails.CustomerBcc = CustBCC;
                    CPDetails.customerCC = CustCC;
                    //added by Kavitha-end
                    CPDetails.workFlowID = WorkFlowID;
                    SMSTemplate[] obj = new SMSTemplate[2];
                    if (hSR.IsDealer == true && branchdata.Branch_Mobile != "" && AssignedTo != 0)
                    {

                        obj = GetSMSDetails(hSR.ServiceRequest_ID, AssignedTo, AddresseType, Convert.ToBoolean(hSR.IsDealer), connString);
                    }
                    else if ((hSR.IsDealer == false || hSR.IsDealer == null) && (AssignedTo != 0))
                    {
                        obj = GetSMSDetails(hSR.ServiceRequest_ID, AssignedTo, AddresseType, Convert.ToBoolean(hSR.IsDealer), connString);

                    }
                    SMSTemplate SMSCustomerObj = obj[0];
                    SMSTemplate SMSAssigneeObj = obj[1];

                    insertWorkFlowHistory(connString, CPDetails, SMSCustomerObj, SMSAssigneeObj, BranchID);
                    //API.insertWorkFlowHistory(RoleID, CompanyID, TransactionNumber, WorkFlowID, CurrentStepID, DateTime.Now, ActionID, UserID, roleID, Common.DecryptString(ActionRemarks), firstRoleorIndividual, DateTime.Now, SMStoAssigneeText, SMStoCustomerText, CustomerMobNo, CustomerEmailID, EmailSub, EmailtoAssigneeBody, EmailtoCustomerBody, EmailSub, CurrentStepID);
                    if (ActionID > 0)
                    {
                        string Region = null;
                        if (hSR.Region_ID != null)
                        {
                            Region = refDetail.Where(i => i.RefMasterDetail_ID == hSR.Region_ID).Select(i => i.RefMasterDetail_Name).FirstOrDefault();
                            //API.insertWorkFlowHistory(RoleID, CompanyID, TransactionNumber, WorkFlowID, CurrentStepID, DateTime.Now, ActionID, UserID, AssignedTo, Common.DecryptString(ActionRemarks), AddresseType, DateTime.Now, SMStoAssigneeText, SMStoCustomerText, CustomerMobNo, CustomerEmailID, EmailSub, EmailtoAssigneeBody, EmailtoCustomerBody, EmailSub, NextStepID);
                        }
                        CPDetails.addresseType = AddresseType;
                        CPDetails.AssignTo = AssignedTo;
                        CPDetails.currentStepID = CurrentStepID;
                        CPDetails.NextStepID = NextStepID;
                        insertWorkFlowHistory(connString, CPDetails, SMSCustomerObj, SMSAssigneeObj, BranchID);

                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM WF_WFField";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new WF_WFField
                                        {
                                            WorkFlow_ID = reader["WorkFlow_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WorkFlow_ID"]),
                                        };

                                        WFFieldList.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }
                        IEnumerable<WF_WFField> FieldList = WFFieldList.Where(i => i.WorkFlow_ID == WorkFlowID);
                        foreach (var x in FieldList)
                        {
                            if (x.WorkFlowFieldName == "Region")
                            {
                                UpdateWorkFlowFieldValue(CompanyID, WorkFlowID, x.WFField_ID, TransactionID, Region, connString);
                            }
                        }
                        ForAutoAllocationList ForAutoAllocationListList = new ForAutoAllocationList
                        {
                            CompanyID = CompanyID,
                            WorkFlowID = WorkFlowID,
                            CurrentStepID = CurrentStepID,
                            ActionID = ActionID,
                            TransactionNumber = TransactionID,
                            RoleID = RoleID,
                            UserID = UserID,
                            UserCulture = UserCulture,
                            BranchID = hSR.Branch_ID,
                        };
                        ForAutoAllocation(ForAutoAllocationListList, connString, LogException);
                        if (isFromWebAPI == false)
                        {
                            UpdateResolution_Response_Time(2, 1, TransactionID, hSR.CallDateAndTime, CompanyID, isFromWebAPI, BranchID, User_ID, CompanyID, HelpDesk, Loggindate, MenuID, HolidayDetails, connString, LogException);
                        }
                        //-------------------//
                        bool isChildInvokeReqd = CheckIsInvokeChildObject(CompanyID, WorkFlowID, CurrentStepID, ActionID, NextStepID, connString);

                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM WF_WFStepLink";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new WF_WFStepLink
                                        {
                                            Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                            FrmWFSteps_ID = reader["FrmWFSteps_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["FrmWFSteps_ID"]),
                                            WorkFlow_ID = reader["WorkFlow_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WorkFlow_ID"]),
                                            WFAction_ID = reader["WFAction_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFAction_ID"]),
                                            ToWFSteps_ID = reader["ToWFSteps_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ToWFSteps_ID"]),
                                        };

                                        WFStepLinkList.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }
                        if (isChildInvokeReqd)
                        {
                            WF_WFStepLink StepL = WFStepLinkList.Where(a => a.Company_ID == CompanyID && a.WorkFlow_ID == WorkFlowID && a.FrmWFSteps_ID == CurrentStepID && a.WFAction_ID == ActionID && a.ToWFSteps_ID == NextStepID).FirstOrDefault();

                            ObjectName = InvokeChildAction(StepL.WFStepLink_ID, connString, LogException);
                        }
                        //-------------------//
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string updateQuery = @"
                                                UPDATE HD_ServiceRequest
                                                SET Current_AssignTo = @AssignedTo
                                                WHERE ServiceRequest_ID = @ServiceRequest_ID";

                            using (var cmd = new SqlCommand(updateQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@AssignedTo", AssignedTo);
                                cmd.Parameters.AddWithValue("@ServiceRequest_ID", hSR.ServiceRequest_ID);

                                cmd.ExecuteNonQuery();
                            }
                        }
                    }

                    //-----------------START-------------------------Parent Work Flow Insert to Case Progress---------------------------------------------------
                    //--------------------------------------------------------------------------------------------------------------------------------------------
                    //Added by Shashi on 9-Jun-2014 for GNM_Sms table changes
                    //CaseProgressObjects PCPDetails = new CaseProgressObjects();
                    //PCPDetails.actionBy = Convert.ToInt32(UserID);
                    //PCPDetails.actionID = PActionID;
                    //PCPDetails.actionRemarks = PActionRemarks;
                    //PCPDetails.actionTime = DateTime.Now;
                    //PCPDetails.addresseType = firstRoleorIndividual;
                    //PCPDetails.AssignTo = roleID;
                    //PCPDetails.CompanyID = Convert.ToInt32(CompanyID);
                    //PCPDetails.currentStepID = PCurrentStepID;
                    //PCPDetails.customerEmailID = CustomerEmailID;
                    //PCPDetails.customerMobileNumber = CustomerMobNo;
                    //PCPDetails.emailBodyAddress = EmailtoAssigneeBody.Replace("\n", "<br/>").ToString();
                    //PCPDetails.emailBodyCustomer = EmailtoCustomerBody.Replace("\n", "<br/>").ToString();
                    //PCPDetails.emailSubAddressee = EmailSub;
                    //PCPDetails.emailSubCustomer = EmailSub;
                    //PCPDetails.NextStepID = PCurrentStepID;
                    //PCPDetails.receivedTime = DateTime.Now;
                    //PCPDetails.RoleID = RoleID;
                    //PCPDetails.smsTextAddressee = SMStoAssigneeText;
                    //PCPDetails.smsTextCustomer = SMStoCustomerText;
                    //PCPDetails.transactionNumber = PSRID;
                    ////added by Kavitha-start
                    //PCPDetails.AddresseBcc = AddresseEmailBCC;
                    //PCPDetails.AddresseCC = AddresseCC;
                    //PCPDetails.CustomerBcc = CustBCC;
                    //PCPDetails.customerCC = CustCC;
                    ////added by Kavitha-end
                    //PCPDetails.workFlowID = WorkFlowID;

                    //API.insertWorkFlowHistory(PCPDetails, SMSCustomerObj, SMSAssigneeObj, BranchID);
                    //if (ActionID > 0)
                    //{
                    //    PCPDetails.addresseType = AddresseType;
                    //    PCPDetails.AssignTo = PAssignedTo;
                    //    PCPDetails.currentStepID = PCurrentStepID;
                    //    PCPDetails.NextStepID = PNextStepID;
                    //    API.insertWorkFlowHistory(PCPDetails, SMSCustomerObj, SMSAssigneeObj, BranchID);
                    //}



                    //--------------------------------------------------------------------------------------------------------------------------------------------
                    //-----------------END-------------------------Parent Work Flow Insert to Case Progress---------------------------------------------------


                    using (var conn = new SqlConnection(connString))
                    {
                        conn.Open();

                        string query = "SELECT * FROM UnregisteredServiceRequestList";

                        using (var cmd = new SqlCommand(query, conn))
                        {
                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var refMasterDetailObj = new HD_UnregisteredServiceRequest
                                    {
                                        UnregisteredServiceRequest_ID = reader["UnregisteredServiceRequest_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["UnregisteredServiceRequest_ID"]),
                                    };

                                    UnregisteredServiceRequestList.Add(refMasterDetailObj);
                                }
                            }
                        }
                    }
                    if (UnRegSRID > 0)
                    {
                        HD_UnregisteredServiceRequest InsRow = UnregisteredServiceRequestList.Where(i => i.UnregisteredServiceRequest_ID == UnRegSRID).FirstOrDefault();
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string updateQuery = @"
                                                UPDATE HD_UnregisteredServiceRequest
                                                SET 
                                                    ServiceRequest_ID = @ServiceRequest_ID,
                                                    Status = @Status
                                                WHERE 
                                                    UnregisteredServiceRequest_ID = @UnregisteredServiceRequest_ID";

                            using (var cmd = new SqlCommand(updateQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@ServiceRequest_ID", hSR.ServiceRequest_ID);
                                cmd.Parameters.AddWithValue("@Status", 3); // Setting status to 3 as per the original code
                                cmd.Parameters.AddWithValue("@UnregisteredServiceRequest_ID", UnRegSRID);
                                cmd.ExecuteNonQuery();
                            }
                        }                        //gbl.InsertGPSDetails(CompanyID, BranchID, UserID, Convert.ToInt32(Common.GetObjectID("HelpDeskUserLandingPage")), UnRegSRID, 0, 0, "Updated" + " " + ServiceRequestNumber, false, Convert.ToInt32(RequestParams[13]), Loggindate, null);
                    }

                    //---------------------------------------------------------------
                    //int SavedServiceRequestID = TransactionID;
                    //string SMP = System.Web.HttpContext.Current.Server.MapPath(AppPath + "/KB_Files");
                    //string SrcPath = SMP + "/" + "Temp_" + User.User_ID;
                    //string DstPath = SMP + "/" + SavedServiceRequestID;

                    //if (Directory.Exists(SrcPath))
                    //{
                    //    if (Reopen == "")
                    //    {
                    //        System.IO.Directory.Move(SrcPath, DstPath);
                    //    }
                    //    else
                    //    {
                    //        string PasteTo = SMP + "\\" + SavedServiceRequestID;
                    //        DirectoryInfo dirFrom = new DirectoryInfo(SrcPath);
                    //        DirectoryInfo dirTo = new DirectoryInfo(PasteTo);
                    //        FileInfo[] files = dirFrom.GetFiles();
                    //        for (int i = 0; i < files.Length; i++)
                    //        {
                    //            files[i].MoveTo(dirTo.FullName + "\\" + files[i].Name);
                    //        }
                    //        dirFrom.Delete(true);
                    //    }
                    //}

                    //if (HDAttachmentData != null)
                    //{
                    //    JObject JAttachments = null;
                    //    List<HelpDesk.Controllers.HelpDeskServiceRequestController.HDAttachment> SDAttachmentList = HDAttachmentData;
                    //    if (SDAttachmentList.Count != 0) JAttachments = JObject.Parse(RequestParams.ElementAt(6)); //JObject.Parse(Request.Params["AttachmentsData"].ToString());

                    //    for (int i = 0; i < SDAttachmentList.Count; i++)
                    //    {
                    //        HD_ServiceRequestAttachmentInfo attachment = new HD_ServiceRequestAttachmentInfo();
                    //        attachment.FileName = SDAttachmentList[i].FileName;
                    //        attachment.FileSize = SDAttachmentList[i].FileSize;
                    //        //Modified By Puneeth M for Call # :- TML-2015020203 on 18-Feb-2015 to set File Size
                    //        //attachment.FileSize = Convert.ToInt32(f[i].Length) > 1024 ? (Convert.ToInt32(f[i].Length) / 1024) : (Convert.ToInt32(f[i].Length));
                    //        //
                    //        attachment.FileUploadDate = Common.LocalTime(BranchID, DateTime.Now);
                    //        attachment.FileUploadedByEmployee_ID = Convert.ToInt32(User.Employee_ID);
                    //        attachment.ServiceRequest_ID = SavedServiceRequestID;

                    //        HD_ServiceRequestAttachmentInfo attach = JAttachments["rows"].ElementAt(i).ToObject<HD_ServiceRequestAttachmentInfo>();
                    //        attachment.FileDescription = Common.DecryptString(attach.FileDescription);
                    //        attachment.IsMailAttachment = attach.IsMailAttachment;

                    //        hSRClient.HD_ServiceRequestAttachmentInfo.Add(attachment);
                    //        hSRClient.SaveChanges();

                    //    }
                    //    HDAttachmentData = null;
                    //}



                    //string SMP = System.Web.HttpContext.Current.Server.MapPath(AppPath + "/Attachments");
                    string SMP = AppPath;
                    int ObjectID = Common.GetObjectID("HelpDeskServiceRequest");
                    if (TransactionID != 0)
                    {
                        int Campaign_ID = TransactionID;
                        if (HDAttachmentData != null)
                        {
                            int History = TransactionID;

                            string SrcPath = string.Empty;

                            List<Attachements> dsattachment = new List<Attachements>();
                            JObject jObj2 = JObject.Parse(RequestParams.ElementAt(6));
                            int Count = jObj2["rows"].Count();
                            Attachements[] ds = new Attachements[Count];
                            for (int i = 0; i < Count; i++)
                            {
                                Attachements detail = new Attachements();
                                ds[i] = detail;
                                JTokenReader reader = null;
                                reader = new JTokenReader(jObj2["rows"][i]["ATTACHMENTDETAIL_ID"]);
                                reader.Read();
                                ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                                reader = new JTokenReader(jObj2["rows"][i]["FILENAME"]);
                                reader.Read();
                                ds[i].FILE_NAME = await _utilityServiceClient.DecryptStringAsync(reader.Value.ToString());
                                reader = new JTokenReader(jObj2["rows"][i]["FILEDESCRIPTION"]);
                                reader.Read();
                                ds[i].FILEDESCRIPTION = await _utilityServiceClient.DecryptStringAsync(reader.Value.ToString());

                                reader = new JTokenReader(jObj2["rows"][i]["Upload"]);
                                reader.Read();
                                ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

                                reader = new JTokenReader(jObj2["rows"][i]["UPLOADDATE"]);
                                reader.Read();
                                ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());


                                reader = new JTokenReader(jObj2["rows"][i]["OBJECT_ID"]);
                                reader.Read();
                                ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());

                                if (isFromWebAPI == false)
                                {
                                    ds[i].DetailID = 0;
                                    string DstPath = SMP + "/" + ds[i].OBJECTID + "-" + TransactionID + "-" + await _utilityServiceClient.DecryptStringAsync(ds[i].FILE_NAME);
                                    SrcPath = SMP + "/" + "Temp_" + objid + "_" + UserID + "/" + await _utilityServiceClient.DecryptStringAsync(ds[i].FILE_NAME);

                                    if (!System.IO.File.Exists(DstPath))
                                    {
                                        System.IO.File.Move(SrcPath, DstPath);

                                    }
                                }

                            }
                            List<Attachements> c = CommonFunctionalities.UploadAttachment(ds, TransactionID, UserID, CompanyID, 0, connString);

                            //Session["AttachmentData"] = null;
                        }


                        List<Attachements> dsattachdelete = new List<Attachements>();
                        JObject jObj1 = new JObject();
                        jObj1 = JObject.Parse(RequestParams.ElementAt(9));
                        int Count1 = jObj1["rows"].Count();
                        Attachements[] ds1 = new Attachements[Count1];
                        for (int i = 0; i < Count1; i++)
                        {
                            Attachements detail = new Attachements();
                            ds1[i] = detail;
                            JTokenReader reader = null;
                            reader = new JTokenReader(jObj1["rows"][i]["id"]);
                            reader.Read();
                            ds1[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                            reader = new JTokenReader(jObj1["rows"][i]["FileName"]);
                            reader.Read();
                            ds1[i].FILE_NAME = await _utilityServiceClient.DecryptStringAsync(reader.Value.ToString());
                            reader = new JTokenReader(jObj1["rows"][i]["Object_ID"]);
                            reader.Read();
                            ds1[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());
                            ds1[i].TransactionID = TransactionID;
                        }
                        CommonFunctionalities.DeleteAttachments(ds1, SMP, connString);

                        int AttCount = CommonFunctionalities.GetAttachmentCount(ObjectID, TransactionID, 0, connString);
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();
                            // Update the AttachmentCount in the database
                            string updateQuery = @"
                                                UPDATE HD_ServiceRequest
                                                SET AttachmentCount = @AttachmentCount
                                                WHERE ServiceRequest_ID = @TransactionID";

                            using (var cmd = new SqlCommand(updateQuery, conn))
                            {
                                // Add parameters for the query
                                cmd.Parameters.AddWithValue("@AttachmentCount", AttCount);
                                cmd.Parameters.AddWithValue("@TransactionID", TransactionID);

                                // Execute the update
                                cmd.ExecuteNonQuery();
                            }
                        }

                    }
                    //------------------------------------------------------------------
                    SaveNotesDetailsList JOBJList = new SaveNotesDetailsList
                    {
                        jObj = jObjx,
                        SRIDGlobal = TransactionID,
                        User_ID = UserID,
                    };
                    HelpDeskSavePartsList jobjyList = new HelpDeskSavePartsList
                    {
                        jObj = jobjy,
                        SRIDGlobal = TransactionID,
                        User_ID = UserID,
                        Branch = BranchID,
                        Company_ID = CompanyID,
                        MenuID = Convert.ToInt32(RequestParams[13])
                    };
                    SaveProductDetailsList jobjProList = new SaveProductDetailsList
                    {
                        jObj = jobjPro,
                        SRIDGlobal = TransactionID,
                        User_ID = UserID,
                        Branch = BranchID,
                        Company_ID = CompanyID,
                        MenuID = Convert.ToInt32(RequestParams[13])
                    };
                    if (NotesData != "")

                        SaveNotesDetails(JOBJList, connString, LogException);
                    if (PartsDetailData != "")
                        SavePartsList(jobjyList, connString, LogException);
                    if (TMLPartsDetailData != "")
                        SaveTMLPartsList(connString, LogException, jobTMlPartsList, TransactionID, User_ID, Convert.ToInt32(RequestParams[13]));
                    if (FollowUpsDetailData != "")
                        SaveFollowUps(jobjz, TransactionID, connString, LogException, UserID, Company_ID, BranchID, MenuID);
                    if (ProductDetailsData != "")
                        SaveProductDetails(jobjProList, connString, LogException);

                    if (QuestionData != "")
                        SaveQuestionDetails(jobjQ, TransactionID, UserID, connString, LogException);
                    //Modified By Puneeth M for Call # :- TML-2015020203 on 18-Feb-2015 to ensure Email Queue date lessa than attachment uploaded data
                    bool IsLostSalesMandatoryVar = false;
                    if (NextStapName == "End")
                    {
                        if (hSR.CaseType_ID == 4)
                        {
                            IsLostSalesMandatoryVar = IsLostSalesMandatory(TransactionID, connString, LogException);
                        }
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            // Define the query to update the record
                            string updateQuery = @"
                                                UPDATE HD_ServiceRequest
                                                SET ClosingDescription = @ClosingDescription,
                                                    CallClosureDateAndTime = @CallClosureDateAndTime
                                                WHERE ServiceRequest_ID = @TransactionID";

                            // Prepare the SQL command
                            using (var cmd = new SqlCommand(updateQuery, conn))
                            {
                                // Add parameters to the SQL command to prevent SQL injection
                                cmd.Parameters.AddWithValue("@ClosingDescription", hSR.ClosingDescription);
                                cmd.Parameters.AddWithValue("@CallClosureDateAndTime", await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString));
                                cmd.Parameters.AddWithValue("@TransactionID", TransactionID);

                                // Execute the update
                                cmd.ExecuteNonQuery();
                            }
                        }
                        //gbl.InsertGPSDetails(CompanyID, BranchID, UserID, Convert.ToInt32(Common.GetObjectID("HelpDeskUserLandingPage")), TransactionID, 0, 0, "Updated" + " " + lastRow.ServiceRequestNumber, false, Convert.ToInt32(RequestParams[13]), Loggindate, null);

                        bool RequestClosureEmail = Convert.ToBoolean(CompParamList.Where(cp => cp.Param_Name.ToUpper() == "REQUESTCLOSUREEMAIL" && cp.Company_ID == CompanyID).Select(cp => cp.Param_value).FirstOrDefault());
                        if (RequestClosureEmail)
                        {
                            InsertServiceRequestClosureEmailForCustomer(ServiceRequestObj, PartyContactPerson, ContactPersonEmail, Convert.ToBoolean(hSR.IsDealer), EmailSub, ServiceRequestNumber, HelpDesk, UserLanguageCode, Company_ID, Employee_ID, Language_ID, connString, LogException);//Send Email to Customer after Service request is closed.
                        }
                        if (isFromWebAPI == false)
                        {
                            UpdateResolution_Response_Time(1, 1, TransactionID, Convert.ToDateTime(hSR.CallDateAndTime), CompanyID, isFromWebAPI, BranchID, User_ID, CompanyID, HelpDesk, Loggindate, MenuID, HolidayDetails, connString, LogException);
                        }

                    }
                    //
                    if (hSR.Model_ID != null && hSR.SerialNumber != null && hSR.IsUnderBreakDown == true)
                    {
                        GNM_RefMaster RefMaster1 = null;

                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string query = "SELECT * FROM GNM_RefMaster";

                            using (var cmd = new SqlCommand(query, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new GNM_RefMaster
                                        {
                                            RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                            RefMaster_Name = reader["RefMaster_Name"] == DBNull.Value ? null : reader["RefMaster_Name"].ToString(),
                                        };

                                        refMasterDetail.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }
                        RefMaster1 = refMasterDetail.Where(rname => rname.RefMaster_Name.ToUpper() == "MACHINESTATUS").FirstOrDefault();
                        int MSBreakdownRefID = refDetail.Where(a => a.RefMaster_ID == RefMaster1.RefMaster_ID && a.RefMasterDetail_Short_Name == "BRK" && a.RefMasterDetail_IsActive == true).Select(i => i.RefMasterDetail_ID).FirstOrDefault();
                        string ProductListquery = "SELECT * FROM GNM_Product";
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();
                            using (var cmd = new SqlCommand(ProductListquery, conn))
                            {
                                using (var reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var refMasterDetailObj = new GNM_Product
                                        {
                                            Product_ID = reader["Product_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Product_ID"]),

                                        };

                                        ProductList.Add(refMasterDetailObj);
                                    }
                                }
                            }
                        }
                        GNM_Product UpdateRow = ProductList.Where(i => i.Product_ID == hSR.Product_ID).FirstOrDefault();
                        if (NextStapName != "End")
                        {
                            if (UpdateRow.MachineStatus_ID != MSBreakdownRefID)  // Checking if Machine Status is Changed if Changed inserting in the Status History Table
                            {
                                int MSID = 0;

                                using (var conn = new SqlConnection(connString))
                                {
                                    conn.Open();

                                    string query = "SELECT * FROM GNM_ProductStatusHistory";

                                    using (var cmd = new SqlCommand(query, conn))
                                    {
                                        using (var reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                var refMasterDetailObj = new GNM_ProductStatusHistory
                                                {
                                                    Product_ID = reader["Product_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Product_ID"]),
                                                    ProductServiceHistory_ID = reader["ProductServiceHistory_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ProductServiceHistory_ID"]),
                                                };

                                                ProductStatusHistoryList.Add(refMasterDetailObj);
                                            }
                                        }
                                    }
                                }
                                GNM_ProductStatusHistory PSHistoryData = ProductStatusHistoryList.Where(p => p.Product_ID == UpdateRow.Product_ID).OrderByDescending(p => p.ProductServiceHistory_ID).FirstOrDefault();
                                if (PSHistoryData != null) { MSID = PSHistoryData.MachineStatus_ID; }
                                if (MSID == 0)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSBreakdownRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSBreakdownRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                                else if (MSID != 0 && MSID != MSBreakdownRefID)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSBreakdownRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSBreakdownRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                        }
                        else if (NextStapName == "End")
                        {
                            int MSRunningRefID = refDetail.Where(a => a.RefMaster_ID == RefMaster1.RefMaster_ID && a.RefMasterDetail_Short_Name == "RUN" && a.RefMasterDetail_IsActive == true).Select(i => i.RefMasterDetail_ID).FirstOrDefault();
                            if (UpdateRow.MachineStatus_ID != MSBreakdownRefID)  // Checking if Machine Status is Changed if Changed inserting in the Status History Table
                            {
                                int MSID = 0;
                                GNM_ProductStatusHistory PSHistoryData = ProductStatusHistoryList.Where(p => p.Product_ID == UpdateRow.Product_ID).OrderByDescending(p => p.ProductServiceHistory_ID).FirstOrDefault();
                                if (PSHistoryData != null) { MSID = PSHistoryData.MachineStatus_ID; }
                                if (MSID == 0)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSRunningRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSRunningRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                                else if (MSID != 0 && MSID != MSRunningRefID)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSRunningRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSRunningRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else if (hSR.Model_ID != null && hSR.SerialNumber != null && hSR.IsUnderBreakDown == false)
                    {
                        GNM_RefMaster RefMaster1 = null;
                        RefMaster1 = refMasterDetail.Where(rname => rname.RefMaster_Name.ToUpper() == "MACHINESTATUS").FirstOrDefault();
                        int MSBreakdownRefID = refDetail.Where(a => a.RefMaster_ID == RefMaster1.RefMaster_ID && a.RefMasterDetail_Short_Name == "MNT" && a.RefMasterDetail_IsActive == true).Select(i => i.RefMasterDetail_ID).FirstOrDefault();

                        GNM_Product UpdateRow = ProductList.Where(i => i.Product_ID == hSR.Product_ID).FirstOrDefault();
                        if (NextStapName != "End")
                        {
                            if (UpdateRow.MachineStatus_ID != MSBreakdownRefID)  // Checking if Machine Status is Changed if Changed inserting in the Status History Table
                            {
                                int MSID = 0;
                                GNM_ProductStatusHistory PSHistoryData = ProductStatusHistoryList.Where(p => p.Product_ID == UpdateRow.Product_ID).OrderByDescending(p => p.ProductServiceHistory_ID).FirstOrDefault();
                                if (PSHistoryData != null) { MSID = PSHistoryData.MachineStatus_ID; }
                                if (MSID == 0)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSBreakdownRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSBreakdownRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                                else if (MSID != 0 && MSID != MSBreakdownRefID)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSBreakdownRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSBreakdownRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                        }
                        else if (NextStapName == "End")
                        {
                            int MSRunningRefID = refDetail.Where(a => a.RefMaster_ID == RefMaster1.RefMaster_ID && a.RefMasterDetail_Short_Name == "RUN" && a.RefMasterDetail_IsActive == true).Select(i => i.RefMasterDetail_ID).FirstOrDefault();
                            if (UpdateRow.MachineStatus_ID != MSBreakdownRefID)  // Checking if Machine Status is Changed if Changed inserting in the Status History Table
                            {
                                int MSID = 0;
                                GNM_ProductStatusHistory PSHistoryData = ProductStatusHistoryList.Where(p => p.Product_ID == UpdateRow.Product_ID).OrderByDescending(p => p.ProductServiceHistory_ID).FirstOrDefault();
                                if (PSHistoryData != null) { MSID = PSHistoryData.MachineStatus_ID; }
                                if (MSID == 0)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSRunningRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSRunningRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                                else if (MSID != 0 && MSID != MSRunningRefID)
                                {
                                    GNM_ProductStatusHistory UpdateHistoryRow = new GNM_ProductStatusHistory();
                                    UpdateHistoryRow.Mode = 3;
                                    UpdateHistoryRow.MobileNumber = ServiceRequestNumber;
                                    UpdateHistoryRow.Product_ID = UpdateRow.Product_ID;
                                    UpdateHistoryRow.ProductStatus_Date = await _utilityServiceClient.LocalTimeAsync(BranchID, DateTime.Now, connString);
                                    UpdateHistoryRow.MachineStatus_ID = Convert.ToInt32(MSRunningRefID);
                                    UpdateHistoryRow.Company_ID = Convert.ToInt32(hSR.Company_ID); ;
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        // Define the query to insert a new record into GNM_ProductStatusHistory
                                        string insertQuery = @"
                                                            INSERT INTO GNM_ProductStatusHistory 
                                                            (Product_ID, Mode, Company_ID, ProductStatus_Date, MobileNumber, MachineStatus_ID)
                                                            VALUES 
                                                            (@Product_ID, @Mode, @Company_ID, @ProductStatus_Date, @MobileNumber, @MachineStatus_ID)";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(insertQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@Product_ID", UpdateHistoryRow.Product_ID);
                                            cmd.Parameters.AddWithValue("@Mode", UpdateHistoryRow.Mode);
                                            cmd.Parameters.AddWithValue("@Company_ID", UpdateHistoryRow.Company_ID);
                                            cmd.Parameters.AddWithValue("@ProductStatus_Date", UpdateHistoryRow.ProductStatus_Date);
                                            cmd.Parameters.AddWithValue("@MobileNumber", UpdateHistoryRow.MobileNumber);
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", UpdateHistoryRow.MachineStatus_ID);

                                            // Execute the insert command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }

                                    //GNM_Product UpdateMachineStatus = ProductClient.GNM_Product.Where(a => a.Model_ID == hSR.Model_ID && a.Product_SerialNumber == hSR.SerialNumber).FirstOrDefault();
                                    using (var conn = new SqlConnection(connString))
                                    {
                                        conn.Open();

                                        string updateQuery = @"
                                                            UPDATE GNM_Product
                                                            SET MachineStatus_ID = @MachineStatus_ID
                                                            WHERE Product_ID = @Product_ID";

                                        // Prepare the SQL command
                                        using (var cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            // Add parameters to the SQL command to prevent SQL injection
                                            cmd.Parameters.AddWithValue("@MachineStatus_ID", MSRunningRefID);
                                            cmd.Parameters.AddWithValue("@ProductServiceHistory_ID", UpdateRow.Product_ID);

                                            // Execute the update command
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                        }

                    }

                    jsonResult = new
                    {
                        IsPSExists = true,
                        Result = "Success",
                        id = TransactionID,
                        ChildAction = ObjectName,
                        CaseType = CaseType_ID,
                        SRNumber = ServiceRequestNumber,
                        PSRID = PSRID,
                        ChildTicket_Sequence_ID,
                        IsLostSalesMandatory = IsLostSalesMandatoryVar,
                    };
                }
                else
                {
                    jsonResult = new
                    {
                        IsPSExists = false,
                        Result = "Success",
                        id = 0,
                        ChildAction = ObjectName,
                        CaseType = CaseType_ID,
                        SRNumber = "",
                        IsLostSalesMandatory = false,
                        PSRID = 0,
                    };
                }
                //scope.Complete();
                //}
            }

            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                    id = 0,
                    IsPSExists = true,
                    ChildAction = ObjectName,
                    CaseType = CaseType_ID,
                    SRNumber = "",
                    IsLostSalesMandatory = false,
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }

        #region ::: To get ProductID:::
        /// <summary>
        /// To get ProductID
        /// </summary>
        /// <returns>...</returns>
        public static int? getProductID(string serialNumber, int? modelID, string constring, int LogException)
        {
            int? productID = null;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to get Product_ID based on the SerialNumber and Model_ID
                    string query = @"SELECT TOP 1 Product_ID 
                             FROM GNM_Product 
                             WHERE Product_SerialNumber = @SerialNumber AND Model_ID = @ModelID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@SerialNumber", serialNumber);
                        cmd.Parameters.AddWithValue("@ModelID", (object)modelID ?? DBNull.Value);  // Handling nullable ModelID

                        // Execute the query and retrieve the result
                        object result = cmd.ExecuteScalar();

                        if (result != null && result != DBNull.Value)
                        {
                            productID = Convert.ToInt32(result);
                            // Set to null if the result is 0 (like your original check)
                            if (productID == 0)
                            {
                                productID = null;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                productID = null;
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return productID;
        }
        #endregion

        public JsonResult GetNextStepType(int companyID, int workFlowID, int stepID, int ActionID, string constring)
        {
            object obj = null;
            dynamic val = obj;
            int toStepType = 0;
            int num = 0;
            string stepTypeName = string.Empty;
            WF_WFStepLink sLink = null;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // If ActionID > 0, fetch data from WF_WFStepLink table
                    if (ActionID > 0)
                    {
                        string queryStepLink = @"
                    SELECT TOP 1 * 
                    FROM GNM_WFStepLink 
                    WHERE FrmWFSteps_ID = @StepID 
                      AND WorkFlow_ID = @WorkFlowID 
                      AND Company_ID = @CompanyID 
                      AND WFAction_ID = @ActionID";

                        using (SqlCommand cmdStepLink = new SqlCommand(queryStepLink, conn))
                        {
                            cmdStepLink.Parameters.AddWithValue("@StepID", stepID);
                            cmdStepLink.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmdStepLink.Parameters.AddWithValue("@CompanyID", companyID);
                            cmdStepLink.Parameters.AddWithValue("@ActionID", ActionID);

                            using (SqlDataReader reader = cmdStepLink.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    sLink = new WF_WFStepLink
                                    {
                                        ToWFSteps_ID = reader.GetInt32(reader.GetOrdinal("ToWFSteps_ID")),
                                        Addresse_WFRole_ID = reader.GetInt32(reader.GetOrdinal("Addresse_WFRole_ID"))
                                    };
                                }
                            }
                        }

                        // Fetch ToStepType, StepStatus, and StepType Name
                        string queryStepDetails = @"
                    SELECT WFStepType_ID, WFStepStatus_ID 
                    FROM GNM_WFSteps 
                    WHERE WFSteps_ID = @ToWFSteps_ID";

                        using (SqlCommand cmdStepDetails = new SqlCommand(queryStepDetails, conn))
                        {
                            cmdStepDetails.Parameters.AddWithValue("@ToWFSteps_ID", sLink.ToWFSteps_ID);

                            using (SqlDataReader reader = cmdStepDetails.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    toStepType = reader.GetInt32(reader.GetOrdinal("WFStepType_ID"));
                                    num = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID"));
                                }
                            }
                        }

                        // Fetch StepType Name
                        string queryStepType = @"
                    SELECT WFStepType_Nm 
                    FROM GNM_WFStepType 
                    WHERE WFStepType_ID = @ToStepType";

                        using (SqlCommand cmdStepType = new SqlCommand(queryStepType, conn))
                        {
                            cmdStepType.Parameters.AddWithValue("@ToStepType", toStepType);

                            using (SqlDataReader reader = cmdStepType.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    stepTypeName = reader.GetString(reader.GetOrdinal("WFStepType_Nm"));
                                }
                            }
                        }

                        // Create result JSON object
                        val = new
                        {
                            NextStepID = sLink.ToWFSteps_ID,
                            ToStepStatus = num,
                            NextStepType = stepTypeName,
                            RoleID = sLink.Addresse_WFRole_ID
                        };
                    }
                    else
                    {
                        // If ActionID is 0, fetch the current step details
                        string queryStepDetails = @"
                    SELECT WFStepType_ID, WFStepStatus_ID 
                    FROM GNM_WFSteps 
                    WHERE WFSteps_ID = @StepID";

                        using (SqlCommand cmdStepDetails = new SqlCommand(queryStepDetails, conn))
                        {
                            cmdStepDetails.Parameters.AddWithValue("@StepID", stepID);

                            using (SqlDataReader reader = cmdStepDetails.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    toStepType = reader.GetInt32(reader.GetOrdinal("WFStepType_ID"));
                                    num = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID"));
                                }
                            }
                        }

                        // Fetch StepType Name
                        string queryStepType = @"
                    SELECT WFStepType_Nm 
                    FROM GNM_WFStepType 
                    WHERE WFStepType_ID = @ToStepType";

                        using (SqlCommand cmdStepType = new SqlCommand(queryStepType, conn))
                        {
                            cmdStepType.Parameters.AddWithValue("@ToStepType", toStepType);

                            using (SqlDataReader reader = cmdStepType.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    stepTypeName = reader.GetString(reader.GetOrdinal("WFStepType_Nm"));
                                }
                            }
                        }

                        // Create result JSON object
                        val = new
                        {
                            NextStepID = stepID,
                            ToStepStatus = num,
                            NextStepType = stepTypeName,
                            RoleID = 0
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                // LS.LogSheetExporter.LogToTextFile(
                //     ex.HResult,
                //     ex.GetType().FullName + ":" + ex.Message,
                //     ex.TargetSite.ToString(),
                //     ex.StackTrace
                // );
                _logger.LogError(ex, "Error in GetNextStepType: {Message}", ex.Message);
            }

            return new JsonResult(val);
        }


        #region :::To Get SMS Text Assignee :::
        /// <summary>
        /// To Get SMS Text Assignee
        /// </summary>
        /// <returns>...</returns>
        public string getSMSTextAssignee(int TransactionID, bool IsDealer, string constring)
        {
            StringBuilder result = new StringBuilder();
            HD_ServiceRequest Srequest = null;
            GNM_Party Party = null;
            // CoreProductMasterServices.GNM_Branch branchdata = new CoreProductMasterServices.GNM_Branch();
            GNM_Branch branchdata = new GNM_Branch();

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch ServiceRequest details
                    string queryServiceRequest = @"
                SELECT ServiceRequestNumber, Party_ID 
                FROM HD_ServiceRequest 
                WHERE ServiceRequest_ID = @TransactionID";

                    using (SqlCommand cmdServiceRequest = new SqlCommand(queryServiceRequest, conn))
                    {
                        cmdServiceRequest.Parameters.AddWithValue("@TransactionID", TransactionID);

                        using (SqlDataReader reader = cmdServiceRequest.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Srequest = new HD_ServiceRequest
                                {
                                    ServiceRequestNumber = reader.GetString(reader.GetOrdinal("ServiceRequestNumber")),
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                };
                            }
                        }
                    }

                    // If IsDealer is true, fetch branch details
                    if (IsDealer)
                    {
                        string queryBranch = @"
                    SELECT Branch_Name, Branch_Location, Branch_Mobile 
                    FROM GNM_Branch 
                    WHERE Branch_ID = @Party_ID";

                        using (SqlCommand cmdBranch = new SqlCommand(queryBranch, conn))
                        {
                            cmdBranch.Parameters.AddWithValue("@Party_ID", Srequest.Party_ID);

                            using (SqlDataReader reader = cmdBranch.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    // branchdata = new CoreProductMasterServices.GNM_Branch
                                    branchdata = new GNM_Branch
                                    {
                                        Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                        Branch_Location = reader.GetString(reader.GetOrdinal("Branch_Location")),
                                        Branch_Mobile = reader.GetString(reader.GetOrdinal("Branch_Mobile"))
                                    };
                                }
                            }
                        }

                        result.Append("Enquiry #: " + Srequest.ServiceRequestNumber + ", ");
                        result.Append("Customer Name: " + branchdata.Branch_Name + " ");
                        result.Append("Location: " + branchdata.Branch_Location + " ");
                        result.Append("Mobile: " + branchdata.Branch_Mobile);
                    }
                    else
                    {
                        // If IsDealer is false, fetch party details
                        string queryParty = @"
                    SELECT Party_Name, Party_Location, Party_Mobile 
                    FROM GNM_Party 
                    WHERE Party_ID = @Party_ID";

                        using (SqlCommand cmdParty = new SqlCommand(queryParty, conn))
                        {
                            cmdParty.Parameters.AddWithValue("@Party_ID", Srequest.Party_ID);

                            using (SqlDataReader reader = cmdParty.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Party = new GNM_Party
                                    {
                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                        Party_Location = reader.GetString(reader.GetOrdinal("Party_Location")),
                                        Party_Mobile = reader.GetString(reader.GetOrdinal("Party_Mobile"))
                                    };
                                }
                            }
                        }

                        result.Append("Enquiry #: " + Srequest.ServiceRequestNumber + ", ");
                        result.Append("Customer Name: " + Party.Party_Name + " ");
                        result.Append("Location: " + Party.Party_Location + " ");
                        result.Append("Mobile: " + Party.Party_Mobile);
                    }
                }
            }
            catch (Exception ex)
            {
                // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in getSMSTextAssignee: {Message}", ex.Message);
            }

            return result.ToString();
        }
        #endregion

        #region :::To Get SMS Text Customer :::
        /// <summary>
        /// To Get SMS Text Assignee
        /// </summary>
        /// <returns>...</returns>
        public string getSMSTextCustomer(int TransactionID, int AssignedTo, byte Addresstype, string constring, int LogException, string HelpLineNumber)
        {
            StringBuilder result = new StringBuilder();
            string AName = string.Empty;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch ServiceRequestNumber from HD_ServiceRequest
                    string queryTranNumber = @"
                SELECT ServiceRequestNumber
                FROM HD_ServiceRequest
                WHERE ServiceRequest_ID = @TransactionID";

                    using (SqlCommand cmdTranNumber = new SqlCommand(queryTranNumber, conn))
                    {
                        cmdTranNumber.Parameters.AddWithValue("@TransactionID", TransactionID);

                        using (SqlDataReader reader = cmdTranNumber.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                result.Append("Dear Customer, your Enquiry # is: " + reader.GetString(reader.GetOrdinal("ServiceRequestNumber")) + ",");
                            }
                        }
                    }

                    // Depending on Addresstype, fetch either User_Name or WFRole_Name
                    if (Addresstype == 1)
                    {
                        string queryUser = @"
                    SELECT User_Name
                    FROM GNM_User
                    WHERE User_ID = @AssignedTo";

                        using (SqlCommand cmdUser = new SqlCommand(queryUser, conn))
                        {
                            cmdUser.Parameters.AddWithValue("@AssignedTo", AssignedTo);

                            using (SqlDataReader reader = cmdUser.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    AName = reader.GetString(reader.GetOrdinal("User_Name"));
                                }
                            }
                        }
                    }
                    else
                    {
                        string queryWFRole = @"
                    SELECT WFRole_Name
                    FROM WF_WFRole
                    WHERE WFRole_ID = @AssignedTo";

                        using (SqlCommand cmdWFRole = new SqlCommand(queryWFRole, conn))
                        {
                            cmdWFRole.Parameters.AddWithValue("@AssignedTo", AssignedTo);

                            using (SqlDataReader reader = cmdWFRole.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    AName = reader.GetString(reader.GetOrdinal("WFRole_Name"));
                                }
                            }
                        }
                    }

                    result.Append("Assigned To: " + AName + ".");
                    result.Append("For any further assistance please call our helpline " + HelpLineNumber + ".");
                    result.Append(" Thank You");
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in getSMSTextCustomer: {Message}", ex.Message);
                }
            }

            return result.ToString();
        }
        #endregion

        #region :::To Get Email Body Assignee :::
        /// <summary>
        /// To Get Email Body Assignee
        /// </summary>
        /// <returns>...</returns>
        public async Task<StringBuilder[]> getEmailBodyAssignee(string constring, int LogException, int TransactionID, string LanguageCode, bool IsDealer, string ReLoginView = "", int UserLangID = 0, string UserCulture = "")
        {
            StringBuilder result = new StringBuilder();
            GNM_Party Party = null;
            GNM_PartyLocale PartyL = null;
            HD_ServiceRequest Srequest = null;
            string BranchName = string.Empty;
            string CompanyName = string.Empty;
            StringBuilder[] templateDetails = null;
            // CoreProductMasterServices.GNM_Branch branchdata = new CoreProductMasterServices.GNM_Branch();
            GNM_Branch branchdata = new GNM_Branch();
            // CoreProductMasterServices.GNM_BranchLocale branchdataL = new CoreProductMasterServices.GNM_BranchLocale();
            GNM_BranchLocale branchdataL = new GNM_BranchLocale();

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch Service Request
                    string querySrequest = @"
                SELECT * 
                FROM HD_ServiceRequest 
                WHERE ServiceRequest_ID = @TransactionID";
                    using (SqlCommand cmdSrequest = new SqlCommand(querySrequest, conn))
                    {
                        cmdSrequest.Parameters.AddWithValue("@TransactionID", TransactionID);
                        using (SqlDataReader reader = cmdSrequest.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Srequest = new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = reader.GetInt32(reader.GetOrdinal("ServiceRequest_ID")),
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    ServiceRequestNumber = reader.GetString(reader.GetOrdinal("ServiceRequestNumber")),
                                    CaseType_ID = reader.GetInt32(reader.GetOrdinal("CaseType_ID")),
                                    CallDescription = reader.GetString(reader.GetOrdinal("CallDescription"))
                                };
                            }
                        }
                    }

                    // Check if Dealer
                    if (IsDealer)
                    {
                        // Check for UserLangCode to determine if we should use Branch or BranchLocale
                        if (string.IsNullOrEmpty(LanguageCode))
                        {
                            string queryBranchData = "SELECT * FROM GNM_Branch WHERE Branch_ID = @Party_ID";
                            using (SqlCommand cmdBranch = new SqlCommand(queryBranchData, conn))
                            {
                                cmdBranch.Parameters.AddWithValue("@Party_ID", Srequest.Party_ID);
                                using (SqlDataReader reader = cmdBranch.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        // branchdata = new CoreProductMasterServices.GNM_Branch
                                        branchdata = new GNM_Branch
                                        {
                                            Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                            Branch_Location = reader.GetString(reader.GetOrdinal("Branch_Location")),
                                            Branch_Mobile = reader.GetString(reader.GetOrdinal("Branch_Mobile")),
                                            Branch_Email = reader.GetString(reader.GetOrdinal("Branch_Email"))
                                        };
                                    }
                                }
                            }
                        }
                        else
                        {
                            string queryBranchDataL = "SELECT * FROM GNM_BranchLocale WHERE Branch_ID = @Party_ID AND Language_ID = @UserLangID";
                            using (SqlCommand cmdBranchL = new SqlCommand(queryBranchDataL, conn))
                            {
                                cmdBranchL.Parameters.AddWithValue("@Party_ID", Srequest.Party_ID);
                                cmdBranchL.Parameters.AddWithValue("@UserLangID", UserLangID);
                                using (SqlDataReader reader = cmdBranchL.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        // branchdataL = new CoreProductMasterServices.GNM_BranchLocale
                                        branchdataL = new GNM_BranchLocale
                                        {
                                            Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                            Branch_Location = reader.GetString(reader.GetOrdinal("Branch_Location")),
                                        };
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        // Fetch Party details
                        string queryPartyData = (string.IsNullOrEmpty(LanguageCode)) ?
                            "SELECT * FROM GNM_Party WHERE Party_ID = @Party_ID" :
                            "SELECT * FROM GNM_PartyLocale WHERE Party_ID = @Party_ID";
                        using (SqlCommand cmdParty = new SqlCommand(queryPartyData, conn))
                        {
                            cmdParty.Parameters.AddWithValue("@Party_ID", Srequest.Party_ID);
                            using (SqlDataReader reader = cmdParty.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Party = new GNM_Party
                                    {
                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                        Party_Location = reader.GetString(reader.GetOrdinal("Party_Location")),
                                        Party_Mobile = reader.GetString(reader.GetOrdinal("Party_Mobile")),
                                        Party_Email = reader.GetString(reader.GetOrdinal("Party_Email"))
                                    };
                                }
                            }
                        }
                    }

                    // Fetch Branch and Company details
                    string queryBranchName = (string.IsNullOrEmpty(LanguageCode)) ?
                        "SELECT Branch_Name FROM GNM_Branch WHERE Branch_ID = @Branch_ID" :
                        "SELECT Branch_Name FROM GNM_BranchLocale WHERE Branch_ID = @Branch_ID AND Language_ID = @UserLangID";

                    using (SqlCommand cmdBranchName = new SqlCommand(queryBranchName, conn))
                    {
                        cmdBranchName.Parameters.AddWithValue("@Branch_ID", Srequest.Branch_ID);
                        cmdBranchName.Parameters.AddWithValue("@UserLangID", UserLangID);
                        BranchName = cmdBranchName.ExecuteScalar().ToString();
                    }

                    string queryCompanyName = (string.IsNullOrEmpty(LanguageCode)) ?
                        "SELECT Company_Name FROM GNM_Company WHERE Company_ID = @Company_ID" :
                        "SELECT Company_Name FROM GNM_CompanyLocale WHERE Company_ID = @Company_ID AND Language_ID = @UserLangID";

                    using (SqlCommand cmdCompanyName = new SqlCommand(queryCompanyName, conn))
                    {
                        cmdCompanyName.Parameters.AddWithValue("@Company_ID", Srequest.Company_ID);
                        cmdCompanyName.Parameters.AddWithValue("@UserLangID", UserLangID);
                        CompanyName = cmdCompanyName.ExecuteScalar().ToString();
                    }

                    // Generate the link for the email
                    string Link = Convert.ToString(ReLoginView) + "?OID=" + GetObjectID(constring, "HelpDeskUserLandingPage") + "&TID=" + TransactionID;

                    // Use the CommonMethodForEmailandSMS to generate the email template
                    if (IsDealer)
                    {
                        CommonMethodForEmailandSMSList emailAndSMSDetails = new CommonMethodForEmailandSMSList
                        {
                            TemplateCode = "EnquiryAssignee",
                            CompanyId = Srequest.Company_ID,
                            LanguageCode = LanguageCode,
                            BranchId = branchdata.Branch_ID,
                            p1 = Srequest.ServiceRequestNumber,
                            p2 = branchdata.Branch_Name,
                            p3 = branchdata.Branch_Location,
                            p4 = branchdata.Branch_Mobile,
                            p5 = branchdata.Branch_Email,
                            p6 = Srequest.CallDescription,
                            p7 = BranchName,
                            p8 = CompanyName,
                            p9 = Link,
                            p10 = (Srequest.CaseType_ID == 1 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Support").ToString() :
                            Srequest.CaseType_ID == 2 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Parts").ToString() :
                            Srequest.CaseType_ID == 3 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Service").ToString() :
                            Srequest.CaseType_ID == 4 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Sales").ToString() : ""),
                        };

                        templateDetails = CommonMethodForEmailandSMS(constring, emailAndSMSDetails);
                    }
                    else
                    {
                        CommonMethodForEmailandSMSList emailAndSMSDetails = new CommonMethodForEmailandSMSList
                        {
                            TemplateCode = "EnquiryAssignee",
                            CompanyId = Srequest.Company_ID,
                            LanguageCode = LanguageCode,
                            BranchId = Party.Party_ID,
                            p1 = Srequest.ServiceRequestNumber,
                            p2 = Party.Party_Name,
                            p3 = Party.Party_Location,
                            p4 = Party.Party_Mobile,
                            p5 = Party.Party_Email,
                            p6 = Srequest.CallDescription,
                            p7 = BranchName,
                            p8 = CompanyName,
                            p9 = Link,
                            p10 = (Srequest.CaseType_ID == 1 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Support").ToString() :
                            Srequest.CaseType_ID == 2 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Parts").ToString() :
                            Srequest.CaseType_ID == 3 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Service").ToString() :
                            Srequest.CaseType_ID == 4 ? _utilityServiceClient.GetResourceStringAsync(UserCulture, "Sales").ToString() : ""),
                        };

                        // Call the method and get the template details
                        templateDetails = CommonMethodForEmailandSMS(constring, emailAndSMSDetails);

                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return templateDetails;
        }
        #endregion

        public int GetObjectID(string connSting, string name)
        {
            int objectID = 0;
            int LogException = Convert.ToInt32(_configuration["LogError"]);
            using (SqlConnection connection = new SqlConnection(connSting))
            {
                try
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("GetObjectIDByName", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add the input parameter for the stored procedure
                        command.Parameters.Add(new SqlParameter("@ObjectName", SqlDbType.NVarChar, 100)).Value = name;

                        // Execute the command and get the result
                        object result = command.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            objectID = Convert.ToInt32(result);
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        // LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        _logger.LogError(ex, "Error in GetObjectID: {Message}", ex.Message);
                    }
                }
            }

            return objectID;
        }

        #region :::To Get Email Body customer :::
        /// <summary>
        /// To Get Email Body Assignee
        /// </summary>
        /// <returns>...</returns>
        public static StringBuilder[] getEmailBodyCustomer(int TransactionID, int AssignedTo, byte Addresstype, bool IsSendPCDMail, string LanguageCode, int Language_ID, int Employee_ID, string constring, int LogException)
        {
            StringBuilder result = new StringBuilder();
            HD_ServiceRequest Srequest = null;
            String BranchName = string.Empty;
            string CompanyName = string.Empty;
            string AName = string.Empty;
            StringBuilder[] templateDetails = null;
            string TranNumber = string.Empty;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch Srequest details
                    string sRequestQuery = "SELECT TOP 1 Branch_ID, Company_ID, ServiceRequestNumber FROM HD_ServiceRequest WHERE ServiceRequest_ID = @TransactionID";
                    int branchId = 0, companyId = 0;

                    using (SqlCommand cmd = new SqlCommand(sRequestQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@TransactionID", TransactionID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                branchId = reader["Branch_ID"] != DBNull.Value ? Convert.ToInt32(reader["Branch_ID"]) : 0;
                                companyId = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0;
                                TranNumber = reader["ServiceRequestNumber"] != DBNull.Value ? reader["ServiceRequestNumber"].ToString() : string.Empty;
                            }
                        }
                    }

                    // Fetch BranchName and CompanyName based on UserLangCode
                    if (string.IsNullOrEmpty(LanguageCode))
                    {
                        // BranchName
                        string branchQuery = "SELECT TOP 1 Branch_Name FROM GNM_Branch WHERE Branch_ID = @BranchID";
                        using (SqlCommand cmd = new SqlCommand(branchQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@BranchID", branchId);
                            BranchName = cmd.ExecuteScalar()?.ToString();
                        }

                        // CompanyName
                        string companyQuery = "SELECT TOP 1 Company_Name FROM GNM_Company WHERE Company_ID = @CompanyID";
                        using (SqlCommand cmd = new SqlCommand(companyQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", companyId);
                            CompanyName = cmd.ExecuteScalar()?.ToString();
                        }
                    }
                    else
                    {
                        // BranchName with localization
                        string branchLocaleQuery = "SELECT TOP 1 Branch_Name FROM GNM_BranchLocale WHERE Branch_ID = @BranchID AND Language_ID = @LanguageID";
                        using (SqlCommand cmd = new SqlCommand(branchLocaleQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@BranchID", branchId);
                            cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                            BranchName = cmd.ExecuteScalar()?.ToString();
                        }

                        // CompanyName with localization
                        string companyLocaleQuery = "SELECT TOP 1 Company_Name FROM GNM_CompanyLocale WHERE Company_ID = @CompanyID AND Language_ID = @LanguageID";
                        using (SqlCommand cmd = new SqlCommand(companyLocaleQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", companyId);
                            cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                            CompanyName = cmd.ExecuteScalar()?.ToString();
                        }
                    }
                }


                if (Addresstype == 1)
                {
                    if (string.IsNullOrEmpty(LanguageCode))
                    {
                        // Query to fetch User_Name from GNM_User
                        string userQuery = "SELECT TOP 1 User_Name FROM GNM_User WHERE User_ID = @AssignedTo";
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand(userQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@AssignedTo", AssignedTo);
                                AName = cmd.ExecuteScalar()?.ToString();
                            }
                        }
                    }
                    else
                    {
                        // Query to fetch User_Name from GNM_UserLocale
                        string userLocaleQuery = "SELECT TOP 1 User_Name FROM GNM_UserLocale WHERE User_ID = @AssignedTo";
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand(userLocaleQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@AssignedTo", AssignedTo);
                                AName = cmd.ExecuteScalar()?.ToString();
                            }
                        }
                    }
                }
                else
                {
                    // Query to fetch WFRole_Name from WF_WFRole
                    string wfRoleQuery = "SELECT TOP 1 WFRole_Name FROM WF_WFRole WHERE WFRole_ID = @AssignedTo";
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand(wfRoleQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@AssignedTo", AssignedTo);
                            AName = cmd.ExecuteScalar()?.ToString();
                        }
                    }
                }

                string CompanyEmployeeName = string.Empty;
                string Model = string.Empty;
                int employee_ID = Convert.ToInt32(Employee_ID == null ? 0 : Employee_ID);
                string IssueArea = string.Empty;
                string IssueSubArea = string.Empty;
                string ProductType = string.Empty;
                string ResponseTime = string.Empty;
                string CallMode = string.Empty;

                if (IsSendPCDMail == true && Srequest.PromisedCompletionDate != null)
                {
                    GNM_PartyContactPersonDetails ContactPerson = new GNM_PartyContactPersonDetails();
                    GNM_Branch branchdata = new GNM_Branch();
                    GNM_BranchLocale branchdataL = new GNM_BranchLocale();
                    if (Srequest.IsDealer == true)
                    {
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            conn.Open();

                            // Define the query based on whether LanguageCode is empty or not
                            string query = string.IsNullOrEmpty(LanguageCode)
                                ? "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @CallModeID"
                                : "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID = @CallModeID AND Language_ID = @LanguageCode";

                            SqlCommand cmd = new SqlCommand(query, conn);
                            cmd.Parameters.AddWithValue("@CallModeID", Srequest.CallMode_ID);
                            if (!string.IsNullOrEmpty(LanguageCode))
                            {
                                cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                            }

                            // Execute the query and get the result
                            SqlDataReader reader = cmd.ExecuteReader();
                            if (reader.Read())
                            {
                                CallMode = reader["RefMasterDetail_Name"].ToString();
                            }
                            reader.Close();
                        }
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            conn.Open();

                            if (string.IsNullOrEmpty(LanguageCode))
                            {
                                SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_Branch WHERE Branch_ID = @PartyID", conn);
                                cmd.Parameters.AddWithValue("@PartyID", Srequest.Party_ID);

                                SqlDataReader reader = cmd.ExecuteReader();
                                if (reader.Read())
                                {
                                    branchdata.Branch_Name = reader["Branch_Name"].ToString();
                                    branchdata.Branch_Mobile = reader["Branch_Mobile"].ToString();
                                }
                                reader.Close();
                            }
                            else
                            {
                                SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_BranchLocale WHERE Branch_ID = @PartyID AND Language_ID = @LanguageCode", conn);
                                cmd.Parameters.AddWithValue("@PartyID", Srequest.Party_ID);
                                cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);

                                SqlDataReader reader = cmd.ExecuteReader();
                                if (reader.Read())
                                {
                                    branchdataL.Branch_Name = reader["Branch_Name"].ToString();
                                }
                                reader.Close();
                            }
                        }
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(constring))
                        {
                            conn.Open();

                            SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_PartyContactPersonDetails WHERE PartyContactPerson_ID = @ContactPersonID", conn);
                            cmd.Parameters.AddWithValue("@ContactPersonID", Srequest.PartyContactPerson_ID);

                            SqlDataReader reader = cmd.ExecuteReader();
                            if (reader.Read())
                            {
                                ContactPerson.PartyContactPerson_Name = reader["PartyContactPerson_Name"].ToString();
                                ContactPerson.PartyContactPerson_Mobile = reader["PartyContactPerson_Mobile"].ToString();
                            }
                            reader.Close();
                        }
                    }

                    // Get Employee name from GNM_CompanyEmployee or GNM_CompanyEmployeeLocale based on LanguageCode
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = string.IsNullOrEmpty(LanguageCode)
                            ? "SELECT Company_Employee_Name FROM GNM_CompanyEmployee WHERE Company_Employee_ID = @EmployeeID"
                            : "SELECT Company_Employee_Name FROM GNM_CompanyEmployeeLocale WHERE Company_Employee_ID = @EmployeeID AND Language_ID = @LanguageCode";

                        SqlCommand cmd = new SqlCommand(query, conn);
                        cmd.Parameters.AddWithValue("@EmployeeID", employee_ID);
                        if (!string.IsNullOrEmpty(LanguageCode))
                        {
                            cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                        }

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            CompanyEmployeeName = reader["Company_Employee_Name"].ToString();
                        }
                        reader.Close();
                    }

                    // Get Model from GNM_Model or GNM_ModelLocale
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = string.IsNullOrEmpty(LanguageCode)
                            ? "SELECT Model_Name FROM GNM_Model WHERE Model_ID = @ModelID"
                            : "SELECT Model_Name FROM GNM_ModelLocale WHERE Model_ID = @ModelID AND Language_ID = @LanguageCode";

                        SqlCommand cmd = new SqlCommand(query, conn);
                        cmd.Parameters.AddWithValue("@ModelID", Srequest.Model_ID);
                        if (!string.IsNullOrEmpty(LanguageCode))
                        {
                            cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                        }

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            Model = reader["Model_Name"].ToString();
                        }
                        reader.Close();
                    }

                    // Get Issue Area from GNM_RefMasterDetail or GNM_RefMasterDetailLocale
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = string.IsNullOrEmpty(LanguageCode)
                            ? "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @IssueAreaID"
                            : "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetailLocale WHERE RefMasterDetail_ID = @IssueAreaID AND Language_ID = @LanguageCode";

                        SqlCommand cmd = new SqlCommand(query, conn);
                        cmd.Parameters.AddWithValue("@IssueAreaID", Srequest.IssueArea_ID);
                        if (!string.IsNullOrEmpty(LanguageCode))
                        {
                            cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                        }

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            IssueArea = reader["RefMasterDetail_Name"].ToString();
                        }
                        reader.Close();
                    }

                    // Get Issue SubArea from HD_IssueSubArea or HD_IssueSubAreaLocale
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = string.IsNullOrEmpty(LanguageCode)
                            ? "SELECT IssueSubArea_Description FROM HD_IssueSubArea WHERE IssueSubArea_ID = @IssueSubAreaID"
                            : "SELECT IssueSubArea_Description FROM HD_IssueSubAreaLocale WHERE IssueSubArea_ID = @IssueSubAreaID AND Language_ID = @LanguageCode";

                        SqlCommand cmd = new SqlCommand(query, conn);
                        cmd.Parameters.AddWithValue("@IssueSubAreaID", Srequest.IssueSubArea_ID);
                        if (!string.IsNullOrEmpty(LanguageCode))
                        {
                            cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                        }

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            IssueSubArea = reader["IssueSubArea_Description"].ToString();
                        }
                        reader.Close();
                    }

                    // Get ProductType from GNM_ProductType or GNM_ProductTypeLocale
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        string query = string.IsNullOrEmpty(LanguageCode)
                            ? "SELECT ProductType_Name FROM GNM_ProductType WHERE ProductType_ID = @ProductTypeID"
                            : "SELECT ProductType_Name FROM GNM_ProductTypeLocale WHERE ProductType_ID = @ProductTypeID AND Language_ID = @LanguageCode";

                        SqlCommand cmd = new SqlCommand(query, conn);
                        cmd.Parameters.AddWithValue("@ProductTypeID", Srequest.ProductType_ID);
                        if (!string.IsNullOrEmpty(LanguageCode))
                        {
                            cmd.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                        }

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            ProductType = reader["ProductType_Name"]?.ToString() ?? string.Empty;
                        }
                        reader.Close();
                    }

                    // Get ResponseTime from HD_ServiceRequest
                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        SqlCommand cmd = new SqlCommand("SELECT ResponseTime FROM HD_ServiceRequest WHERE ServiceRequest_ID = @ServiceRequestID", conn);
                        cmd.Parameters.AddWithValue("@ServiceRequestID", Srequest.ServiceRequest_ID);

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            ResponseTime = reader["ResponseTime"]?.ToString() ?? string.Empty;
                        }
                        reader.Close();
                    }
                    ResponseTime = ResponseTime == null ? string.Empty : ResponseTime;

                    StringBuilder SerialNumbersb = new StringBuilder();
                    SerialNumbersb.Append("<tr>");
                    SerialNumbersb.Append("<td style='width: 200px; text-align: left; border-top: none; border-left: solid windowtext 2.25pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 2.25pt; background: #D9D9D9; padding: 0in 5.4pt 0in 5.4pt;'><nobr><b>Serial #</b></nobr></td>");
                    SerialNumbersb.Append("<td style='width: 500px; text-align: left; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 2.25pt; padding: 0in 5.4pt 0in 5.4pt;'>&nbsp;" + Srequest.Flexi1 + "</td>");
                    SerialNumbersb.Append("</tr>");
                    if (Srequest.IsDealer == true)
                    {
                        // Populate the CommonMethodForEmailandSMSList object
                        CommonMethodForEmailandSMSList emailAndSmsParams = new CommonMethodForEmailandSMSList
                        {
                            TemplateCode = "EnquiryPCDToCustomer",
                            CompanyId = Srequest.Company_ID,
                            LanguageCode = LanguageCode,
                            BranchId = Srequest.Branch_ID,
                            p1 = TranNumber,
                            p2 = Srequest.CallDateAndTime.ToString("dd-MMM-yyyy hh:mm tt"),
                            p3 = CallMode,
                            p4 = (LanguageCode == "")
                            ? branchdata.Branch_Name + "," + branchdata.Branch_Mobile
                            : branchdataL.Branch_Name + "," + branchdata.Branch_Mobile,
                            p5 = CompanyEmployeeName,
                            p6 = Model,
                            p7 = Srequest.SerialNumber,
                            p8 = Srequest.CallDescription,
                            p9 = IssueSubArea,
                            p10 = BranchName,
                            p11 = CompanyName,
                            p12 = (Srequest.Flexi1 != null && Srequest.Flexi1 != string.Empty) ? SerialNumbersb.ToString() : string.Empty,
                            p13 = Convert.ToDateTime(Srequest.PromisedCompletionDate).ToString("dd-MMM-yyyy hh:mm tt"),
                            p14 = ResponseTime,
                            p15 = IssueArea,
                            p16 = ProductType
                        };

                        // Call the refactored method
                        templateDetails = CommonMethodForEmailandSMS(constring, emailAndSmsParams);

                    }
                    else
                    {
                        // Create and populate the CommonMethodForEmailandSMSList object
                        CommonMethodForEmailandSMSList emailAndSmsParams = new CommonMethodForEmailandSMSList
                        {
                            TemplateCode = "EnquiryPCDToCustomer",
                            CompanyId = Srequest.Company_ID,
                            LanguageCode = LanguageCode,
                            BranchId = Srequest.Branch_ID,
                            p1 = TranNumber,
                            p2 = Srequest.CallDateAndTime.ToString("dd-MMM-yyyy hh:mm tt"),
                            p3 = CallMode,
                            p4 = ContactPerson.PartyContactPerson_Name + "," + ContactPerson.PartyContactPerson_Mobile,
                            p5 = CompanyEmployeeName,
                            p6 = Model,
                            p7 = Srequest.SerialNumber,
                            p8 = Srequest.CallDescription,
                            p9 = IssueSubArea,
                            p10 = BranchName,
                            p11 = CompanyName,
                            p12 = (Srequest.Flexi1 != null && Srequest.Flexi1 != string.Empty) ? SerialNumbersb.ToString() : string.Empty,
                            p13 = Convert.ToDateTime(Srequest.PromisedCompletionDate).ToString("dd-MMM-yyyy hh:mm tt"),
                            p14 = ResponseTime,
                            p15 = IssueArea,
                            p16 = ProductType
                        };

                        // Call the refactored method
                        templateDetails = CommonMethodForEmailandSMS(constring, emailAndSmsParams);

                    }
                }
                else
                {
                    // Create and populate the CommonMethodForEmailandSMSList object
                    CommonMethodForEmailandSMSList emailAndSmsParams = new CommonMethodForEmailandSMSList
                    {
                        TemplateCode = "EnquiryCustomer",
                        CompanyId = Srequest.Company_ID,
                        LanguageCode = LanguageCode,
                        BranchId = Srequest.Branch_ID,
                        p1 = TranNumber,
                        p2 = BranchName,
                        p3 = CompanyName
                    };

                    // Call the refactored method
                    templateDetails = CommonMethodForEmailandSMS(constring, emailAndSmsParams);

                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return templateDetails;
        }
        #endregion
        public static void InsertCustomerComplaintRegistationEmailandSMS(HD_ServiceRequest ServiceRequest, GNM_PartyContactPersonDetails ContactPerson, string ContactPersonEmail, bool IsDealer, string ContactPersonMobile, string EmailSubject, string ServiceRequestNumber, string LanguageCode, int Company_ID, int Employee_ID, string connString, int LogException)
        {
            StringBuilder result = new StringBuilder();
            string BranchName = string.Empty;
            string CompanyNane = string.Empty;
            string AName = string.Empty;
            string ProductType = string.Empty;
            try
            {
                List<CoreProductMasterServices.GNM_Branch> BranchList = new List<CoreProductMasterServices.GNM_Branch>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Branch";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Branch
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? null : reader["Branch_Name"].ToString(),
                                };

                                BranchList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_Company> CompanyList = new List<GNM_Company>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Company";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new CoreProductMasterServices.GNM_Company
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"] == DBNull.Value ? null : reader["Company_Name"].ToString(),
                                };

                                CompanyList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMasterDetail";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetail
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),
                                };

                                refDetail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<CoreProductMasterServices.GNM_CompanyEmployee> companyEmployees = new List<CoreProductMasterServices.GNM_CompanyEmployee>();
                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_CompanyEmployee";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var EmployeeObj = new CoreProductMasterServices.GNM_CompanyEmployee
                                {
                                    Company_Employee_ID = reader["Company_Employee_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_Employee_ID"]),
                                    Company_Employee_Active = reader["Company_Employee_Active"] == DBNull.Value ? false : Convert.ToBoolean(reader["Company_Employee_Active"]),
                                    Company_Employee_Name = reader["Company_Employee_Name"] == DBNull.Value ? null : reader["Company_Employee_Name"].ToString(),

                                };

                                companyEmployees.Add(EmployeeObj);
                            }
                        }
                    }
                }
                List<CoreModelMasterServices.GNM_Model> Model_Detail = new List<CoreModelMasterServices.GNM_Model>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Model";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new CoreModelMasterServices.GNM_Model
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                    Model_Name = reader["Model_Name"] == DBNull.Value ? null : reader["Model_Name"].ToString(),
                                };

                                Model_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<HD_IssueSubArea> IssueSubAreaList = new List<HD_IssueSubArea>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM HD_IssueSubArea";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_IssueSubArea
                                {
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    IssueSubArea_Description = reader["IssueSubArea_Description"] == DBNull.Value ? null : reader["IssueSubArea_Description"].ToString(),
                                    IssueSubArea_IsActive = (bool)(reader["IssueSubArea_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IssueSubArea_IsActive"])),
                                };

                                IssueSubAreaList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<CoreProductTypeMasterServices.GNM_ProductType> productTypeList = new List<CoreProductTypeMasterServices.GNM_ProductType>();
                string GNM_ProductType = "SELECT * FROM GNM_ProductType;";
                using (SqlConnection connection = new SqlConnection(connString))
                {

                    SqlCommand command = new SqlCommand(GNM_ProductType, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            while (reader.Read())
                            {
                                CoreProductTypeMasterServices.GNM_ProductType productType = new CoreProductTypeMasterServices.GNM_ProductType
                                {
                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                    Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                    ProductType_Name = reader.IsDBNull(reader.GetOrdinal("ProductType_Name")) ? null : reader.GetString(reader.GetOrdinal("ProductType_Name")),
                                    ProductType_IsActive = reader.GetBoolean(reader.GetOrdinal("ProductType_IsActive")),
                                    ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                    ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                };

                                // Add the created object to the list
                                productTypeList.Add(productType);
                            }
                        }
                    }
                    reader.Close();
                }
                List<GNM_CompParam> CompParamList = new List<GNM_CompParam>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_CompParam";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_CompParam
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Param_Name = reader["Param_Name"] == DBNull.Value ? null : reader["Param_Name"].ToString(),
                                    Param_value = reader["Param_value"] == DBNull.Value ? null : reader["Param_value"].ToString(),
                                };

                                CompParamList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                if (ContactPerson.PartyContactPerson_Email.Trim() != "" && (IsDealer == false || IsDealer == null))
                {

                    BranchName = BranchList.Where(b => b.Company_ID == ServiceRequest.Company_ID && b.Branch_ID == ServiceRequest.Branch_ID).Select(i => i.Branch_Name).FirstOrDefault();
                    CompanyNane = CompanyList.Where(c => c.Company_ID == ServiceRequest.Company_ID).Select(i => i.Company_Name).FirstOrDefault();
                    string CallMode = refDetail.Where(R => R.RefMasterDetail_ID == ServiceRequest.CallMode_ID).Select(S => S.RefMasterDetail_Name).FirstOrDefault();
                    string CompanyEmployeeName = companyEmployees.Where(E => E.Company_Employee_ID == Employee_ID).Select(S => S.Company_Employee_Name).FirstOrDefault();
                    string Model = Model_Detail.Where(M => M.Model_ID == ServiceRequest.Model_ID).Select(S => S.Model_Name).FirstOrDefault();
                    string IssueSubArea = IssueSubAreaList.Where(I => I.IssueSubArea_ID == ServiceRequest.IssueSubArea_ID).Select(S => S.IssueSubArea_Description).FirstOrDefault();
                    string IssueArea = refDetail.Where(i => i.RefMasterDetail_ID == ServiceRequest.IssueArea_ID).Select(i => i.RefMasterDetail_Name).FirstOrDefault();
                    ProductType = productTypeList.Where(i => i.ProductType_ID == ServiceRequest.ProductType_ID).Select(i => i.ProductType_Name).FirstOrDefault();
                    ProductType = ProductType == null ? string.Empty : ProductType;

                    StringBuilder SerialNumbersb = new StringBuilder();
                    SerialNumbersb.Append("<tr>");
                    SerialNumbersb.Append("<td style='width: 200px; text-align: left; border-top: none; border-left: solid windowtext 2.25pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 2.25pt; background: #D9D9D9; padding: 0in 5.4pt 0in 5.4pt;'><nobr><b>Serial #</b></nobr></td>");
                    SerialNumbersb.Append("<td style='width: 500px; text-align: left; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 2.25pt; padding: 0in 5.4pt 0in 5.4pt;'>&nbsp;" + ServiceRequest.Flexi1 + "</td>");
                    SerialNumbersb.Append("</tr>");


                    // Populate the CommonMethodForEmailandSMSList object
                    var emailAndSMSDetails = new CommonMethodForEmailandSMSList
                    {
                        TemplateCode = "EnquiryACK",
                        CompanyId = ServiceRequest.Company_ID,
                        LanguageCode = LanguageCode,
                        p1 = ServiceRequestNumber,
                        p2 = ServiceRequest.CallDateAndTime.ToString("dd-MMM-yyyy hh:mm tt"),
                        p3 = CallMode,
                        p4 = ContactPerson.PartyContactPerson_Name + ", " + ContactPerson.PartyContactPerson_Mobile,
                        p5 = CompanyEmployeeName,
                        p6 = Model,
                        p7 = ServiceRequest.SerialNumber,
                        p8 = ServiceRequest.CallDescription,
                        p9 = IssueSubArea,
                        p10 = BranchName,
                        p11 = CompanyNane,
                        p12 = (ServiceRequest.Flexi1 != null && ServiceRequest.Flexi1 != string.Empty) ? SerialNumbersb.ToString() : string.Empty,
                        p13 = IssueArea,
                        p14 = ProductType,
                        p15 = (ServiceRequest.CaseType_ID == 1 ? "Support" : ServiceRequest.CaseType_ID == 2 ? "Parts" : ServiceRequest.CaseType_ID == 3 ? "Service" : ServiceRequest.CaseType_ID == 4 ? "Sales" : "")
                    };

                    // Call the method with the connection string and populated object
                    StringBuilder[] templateDetails = CommonMethodForEmailandSMS(connString, emailAndSMSDetails);

                    EmailSubject = templateDetails[0].ToString();
                    string EmailBody = templateDetails[1].ToString();
                    WF_Email NewMail = new WF_Email();
                    NewMail.Email_Subject = EmailSubject;
                    NewMail.Email_Body = EmailBody.Replace("\n", "<br/>").ToString();
                    //added by Kavitha-start
                    NewMail.Email_Bcc = templateDetails[3].ToString();
                    NewMail.Email_cc = templateDetails[4].ToString();
                    //added by Kavitha-end
                    //NewMail.Email_Queue_Date = DateTime.Now;
                    NewMail.Email_Queue_Date = localTime;  //Added by Venkateshwari for HelpDesk CR-5 Changes 13-Aug-2015.
                    NewMail.Email_Sent_Date = null;
                    NewMail.Email_SentStatus = false;
                    NewMail.Email_IsError = false;
                    NewMail.NoOfAttempts = 0;

                    GNM_CompParam RegistrationMailIDs = CompParamList.Where(i => i.Company_ID == Company_ID && i.Param_Name.ToUpper() == "REGISTRATIONEMAILID").FirstOrDefault();
                    if (RegistrationMailIDs.Param_value != "" && RegistrationMailIDs.Param_value != null)
                    {
                        string EmailIds = RegistrationMailIDs.Param_value;
                        string[] EmailID = EmailIds.Split(',');
                        for (int i = 0; i < EmailID.Count(); i++)
                        {
                            NewMail.Email_To = EmailID[i];
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                string query = @"
                                    INSERT INTO WF_Email 
                                        (Email_Subject, Email_Body, Email_To, Email_cc, Email_Bcc, 
                                         Email_Queue_Date, Email_Sent_Date, Email_SentStatus, Email_Attachments, 
                                         Email_IsError, NoOfAttempts) 
                                    VALUES 
                                        (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                         @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, @Email_Attachments, 
                                         @Email_IsError, @NoOfAttempts);
                                ";

                                using (SqlCommand command = new SqlCommand(query, connection))
                                {
                                    // Add parameters to prevent SQL injection
                                    command.Parameters.AddWithValue("@Email_Subject", NewMail.Email_Subject ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Body", NewMail.Email_Body ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_To", NewMail.Email_To ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_cc", NewMail.Email_cc ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Bcc", NewMail.Email_Bcc ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Queue_Date", NewMail.Email_Queue_Date ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Sent_Date", NewMail.Email_Sent_Date ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_SentStatus", NewMail.Email_SentStatus);
                                    command.Parameters.AddWithValue("@Email_Attachments", NewMail.Email_Attachments ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_IsError", NewMail.Email_IsError ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@NoOfAttempts", NewMail.NoOfAttempts ?? (object)DBNull.Value);

                                    // Open connection and execute the query
                                    connection.Open();
                                    command.ExecuteNonQuery();
                                }
                            }

                        }
                        NewMail.Email_To = ContactPerson.PartyContactPerson_Email;
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            string query = @"
                                INSERT INTO WF_Email 
                                    (Email_Subject, Email_Body, Email_To, Email_cc, Email_Bcc, 
                                     Email_Queue_Date, Email_Sent_Date, Email_SentStatus, Email_Attachments, 
                                     Email_IsError, NoOfAttempts) 
                                VALUES 
                                    (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                     @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, @Email_Attachments, 
                                     @Email_IsError, @NoOfAttempts);
                            ";

                            using (SqlCommand command = new SqlCommand(query, connection))
                            {
                                // Add parameters to prevent SQL injection
                                command.Parameters.AddWithValue("@Email_Subject", NewMail.Email_Subject ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Body", NewMail.Email_Body ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_To", NewMail.Email_To ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_cc", NewMail.Email_cc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Bcc", NewMail.Email_Bcc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Queue_Date", NewMail.Email_Queue_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Sent_Date", NewMail.Email_Sent_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_SentStatus", NewMail.Email_SentStatus);
                                command.Parameters.AddWithValue("@Email_Attachments", NewMail.Email_Attachments ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_IsError", NewMail.Email_IsError ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@NoOfAttempts", NewMail.NoOfAttempts ?? (object)DBNull.Value);

                                // Open connection and execute the query
                                connection.Open();
                                command.ExecuteNonQuery();
                            }
                        }

                    }
                    else
                    {
                        NewMail.Email_To = ContactPerson.PartyContactPerson_Email;
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            string query = @"
                                INSERT INTO WF_Email 
                                    (Email_Subject, Email_Body, Email_To, Email_cc, Email_Bcc, 
                                     Email_Queue_Date, Email_Sent_Date, Email_SentStatus, Email_Attachments, 
                                     Email_IsError, NoOfAttempts) 
                                VALUES 
                                    (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                     @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, @Email_Attachments, 
                                     @Email_IsError, @NoOfAttempts);
                            ";

                            using (SqlCommand command = new SqlCommand(query, connection))
                            {
                                // Add parameters to prevent SQL injection
                                command.Parameters.AddWithValue("@Email_Subject", NewMail.Email_Subject ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Body", NewMail.Email_Body ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_To", NewMail.Email_To ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_cc", NewMail.Email_cc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Bcc", NewMail.Email_Bcc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Queue_Date", NewMail.Email_Queue_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Sent_Date", NewMail.Email_Sent_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_SentStatus", NewMail.Email_SentStatus);
                                command.Parameters.AddWithValue("@Email_Attachments", NewMail.Email_Attachments ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_IsError", NewMail.Email_IsError ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@NoOfAttempts", NewMail.NoOfAttempts ?? (object)DBNull.Value);

                                // Open connection and execute the query
                                connection.Open();
                                command.ExecuteNonQuery();
                            }
                        }

                    }
                }
                else if (ContactPersonEmail.Trim() != "" && (IsDealer == true))
                {

                    BranchName = BranchList.Where(b => b.Company_ID == ServiceRequest.Company_ID && b.Branch_ID == ServiceRequest.Branch_ID).Select(i => i.Branch_Name).FirstOrDefault();
                    CompanyNane = CompanyList.Where(c => c.Company_ID == ServiceRequest.Company_ID).Select(i => i.Company_Name).FirstOrDefault();
                    string CallMode = refDetail.Where(R => R.RefMasterDetail_ID == ServiceRequest.CallMode_ID).Select(S => S.RefMasterDetail_Name).FirstOrDefault();
                    string CompanyEmployeeName = companyEmployees.Where(E => E.Company_Employee_ID == Employee_ID).Select(S => S.Company_Employee_Name).FirstOrDefault();
                    string Model = Model_Detail.Where(M => M.Model_ID == ServiceRequest.Model_ID).Select(S => S.Model_Name).FirstOrDefault();
                    string IssueSubArea = IssueSubAreaList.Where(I => I.IssueSubArea_ID == ServiceRequest.IssueSubArea_ID).Select(S => S.IssueSubArea_Description).FirstOrDefault();
                    string IssueArea = refDetail.Where(i => i.RefMasterDetail_ID == ServiceRequest.IssueArea_ID).Select(i => i.RefMasterDetail_Name).FirstOrDefault();
                    ProductType = productTypeList.Where(i => i.ProductType_ID == ServiceRequest.ProductType_ID).Select(i => i.ProductType_Name).FirstOrDefault();
                    ProductType = ProductType == null ? string.Empty : ProductType;

                    StringBuilder SerialNumbersb = new StringBuilder();
                    SerialNumbersb.Append("<tr>");
                    SerialNumbersb.Append("<td style='width: 200px; text-align: left; border-top: none; border-left: solid windowtext 2.25pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 2.25pt; background: #D9D9D9; padding: 0in 5.4pt 0in 5.4pt;'><nobr><b>Serial #</b></nobr></td>");
                    SerialNumbersb.Append("<td style='width: 500px; text-align: left; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 2.25pt; padding: 0in 5.4pt 0in 5.4pt;'>&nbsp;" + ServiceRequest.Flexi1 + "</td>");
                    SerialNumbersb.Append("</tr>");

                    // Populate the CommonMethodForEmailandSMSList object
                    var emailAndSMSDetails = new CommonMethodForEmailandSMSList
                    {
                        TemplateCode = "EnquiryACK",
                        CompanyId = ServiceRequest.Company_ID,
                        LanguageCode = LanguageCode,
                        p1 = ServiceRequestNumber,
                        p2 = ServiceRequest.CallDateAndTime.ToString("dd-MMM-yyyy hh:mm tt"),
                        p3 = CallMode,
                        p4 = ContactPerson.PartyContactPerson_Name + ", " + ContactPerson.PartyContactPerson_Mobile,
                        p5 = CompanyEmployeeName,
                        p6 = Model,
                        p7 = ServiceRequest.SerialNumber,
                        p8 = ServiceRequest.CallDescription,
                        p9 = IssueSubArea,
                        p10 = BranchName,
                        p11 = CompanyNane,
                        p12 = (ServiceRequest.Flexi1 != null && ServiceRequest.Flexi1 != string.Empty) ? SerialNumbersb.ToString() : string.Empty,
                        p13 = IssueArea,
                        p14 = ProductType,
                        p15 = (ServiceRequest.CaseType_ID == 1 ? "Support" : ServiceRequest.CaseType_ID == 2 ? "Parts" : ServiceRequest.CaseType_ID == 3 ? "Service" : ServiceRequest.CaseType_ID == 4 ? "Sales" : "")
                    };

                    // Call the method with the connection string and populated object
                    StringBuilder[] templateDetails = CommonMethodForEmailandSMS(connString, emailAndSMSDetails);

                    EmailSubject = templateDetails[0].ToString();
                    string EmailBody = templateDetails[1].ToString();
                    WF_Email NewMail = new WF_Email();
                    NewMail.Email_Subject = EmailSubject;
                    NewMail.Email_Body = EmailBody.Replace("\n", "<br/>").ToString();
                    //added by Kavitha-start
                    NewMail.Email_Bcc = templateDetails[3].ToString();
                    NewMail.Email_cc = templateDetails[4].ToString();
                    //added by Kavitha-end                    
                    NewMail.Email_Queue_Date = localTime;  //Added by Venkateshwari for HelpDesk CR-5 Changes 13-Aug-2015.
                    NewMail.Email_Sent_Date = null;
                    NewMail.Email_SentStatus = false;
                    NewMail.Email_IsError = false;
                    NewMail.NoOfAttempts = 0;
                    GNM_CompParam RegistrationMailIDs = CompParamList.Where(i => i.Company_ID == Company_ID && i.Param_Name.ToUpper() == "REGISTRATIONEMAILID").FirstOrDefault();
                    if (RegistrationMailIDs.Param_value != "" && RegistrationMailIDs.Param_value != null)
                    {
                        string EmailIds = RegistrationMailIDs.Param_value;
                        string[] EmailID = EmailIds.Split(',');
                        for (int i = 0; i < EmailID.Count(); i++)
                        {
                            NewMail.Email_To = EmailID[i];
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                string query = @"
                                    INSERT INTO WF_Email 
                                        (Email_Subject, Email_Body, Email_To, Email_cc, Email_Bcc, 
                                         Email_Queue_Date, Email_Sent_Date, Email_SentStatus, Email_Attachments, 
                                         Email_IsError, NoOfAttempts) 
                                    VALUES 
                                        (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                         @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, @Email_Attachments, 
                                         @Email_IsError, @NoOfAttempts);
                                ";

                                using (SqlCommand command = new SqlCommand(query, connection))
                                {
                                    // Add parameters to prevent SQL injection
                                    command.Parameters.AddWithValue("@Email_Subject", NewMail.Email_Subject ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Body", NewMail.Email_Body ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_To", NewMail.Email_To ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_cc", NewMail.Email_cc ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Bcc", NewMail.Email_Bcc ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Queue_Date", NewMail.Email_Queue_Date ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_Sent_Date", NewMail.Email_Sent_Date ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_SentStatus", NewMail.Email_SentStatus);
                                    command.Parameters.AddWithValue("@Email_Attachments", NewMail.Email_Attachments ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Email_IsError", NewMail.Email_IsError ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@NoOfAttempts", NewMail.NoOfAttempts ?? (object)DBNull.Value);

                                    // Open connection and execute the query
                                    connection.Open();
                                    command.ExecuteNonQuery();
                                }
                            }

                        }
                        NewMail.Email_To = ContactPersonEmail;
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            string query = @"
                                INSERT INTO WF_Email 
                                    (Email_Subject, Email_Body, Email_To, Email_cc, Email_Bcc, 
                                     Email_Queue_Date, Email_Sent_Date, Email_SentStatus, Email_Attachments, 
                                     Email_IsError, NoOfAttempts) 
                                VALUES 
                                    (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                     @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, @Email_Attachments, 
                                     @Email_IsError, @NoOfAttempts);
                            ";

                            using (SqlCommand command = new SqlCommand(query, connection))
                            {
                                // Add parameters to prevent SQL injection
                                command.Parameters.AddWithValue("@Email_Subject", NewMail.Email_Subject ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Body", NewMail.Email_Body ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_To", NewMail.Email_To ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_cc", NewMail.Email_cc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Bcc", NewMail.Email_Bcc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Queue_Date", NewMail.Email_Queue_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Sent_Date", NewMail.Email_Sent_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_SentStatus", NewMail.Email_SentStatus);
                                command.Parameters.AddWithValue("@Email_Attachments", NewMail.Email_Attachments ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_IsError", NewMail.Email_IsError ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@NoOfAttempts", NewMail.NoOfAttempts ?? (object)DBNull.Value);

                                // Open connection and execute the query
                                connection.Open();
                                command.ExecuteNonQuery();
                            }
                        }

                    }
                    else
                    {
                        NewMail.Email_To = ContactPersonEmail;
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            string query = @"
                                INSERT INTO WF_Email 
                                    (Email_Subject, Email_Body, Email_To, Email_cc, Email_Bcc, 
                                     Email_Queue_Date, Email_Sent_Date, Email_SentStatus, Email_Attachments, 
                                     Email_IsError, NoOfAttempts) 
                                VALUES 
                                    (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                     @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, @Email_Attachments, 
                                     @Email_IsError, @NoOfAttempts);
                            ";

                            using (SqlCommand command = new SqlCommand(query, connection))
                            {
                                // Add parameters to prevent SQL injection
                                command.Parameters.AddWithValue("@Email_Subject", NewMail.Email_Subject ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Body", NewMail.Email_Body ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_To", NewMail.Email_To ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_cc", NewMail.Email_cc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Bcc", NewMail.Email_Bcc ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Queue_Date", NewMail.Email_Queue_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_Sent_Date", NewMail.Email_Sent_Date ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_SentStatus", NewMail.Email_SentStatus);
                                command.Parameters.AddWithValue("@Email_Attachments", NewMail.Email_Attachments ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Email_IsError", NewMail.Email_IsError ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@NoOfAttempts", NewMail.NoOfAttempts ?? (object)DBNull.Value);

                                // Open connection and execute the query
                                connection.Open();
                                command.ExecuteNonQuery();
                            }
                        }

                    }

                }
                if (ContactPerson.PartyContactPerson_Mobile != "" && (IsDealer == false || IsDealer == null))
                {
                    ProductType = productTypeList.Where(P => P.ProductType_ID == ServiceRequest.ProductType_ID).Select(S => S.ProductType_Name).FirstOrDefault();
                    WF_Sms SmSObj = new WF_Sms();
                    SmSObj.Sms_Text = "Your request is registered with Call Number " + ServiceRequestNumber + ", Product Type:" + ProductType + ". We will get in touch with you shortly.";
                    SmSObj.Sms_Mobile_Number = ContactPerson.PartyContactPerson_Mobile;
                    SmSObj.Sms_Queue_Date = localTime;  //Added by Venkateshwari for HelpDesk CR-5 Changes 13-Aug-2015.
                    SmSObj.Sms_Sent_Date = null;
                    SmSObj.Sms_SentStatus = false;
                    SmSObj.Template_ID = 1;
                    if (ProductType != "")
                    {
                        SmSObj.Parameter1_value = ServiceRequestNumber + ", " + ProductType + " ";
                    }
                    else
                    {
                        SmSObj.Parameter1_value = ServiceRequestNumber + " ";
                    }
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        string query = @"
                            INSERT INTO WF_Sms 
                                (Sms_Text, Sms_Mobile_Number, Sms_Queue_Date, Sms_Sent_Date, 
                                 Sms_SentStatus, Template_ID, Parameter1_value, Parameter2_value, 
                                 Parameter3_value, Parameter4_value) 
                            VALUES 
                                (@Sms_Text, @Sms_Mobile_Number, @Sms_Queue_Date, @Sms_Sent_Date, 
                                 @Sms_SentStatus, @Template_ID, @Parameter1_value, @Parameter2_value, 
                                 @Parameter3_value, @Parameter4_value);
                        ";

                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            // Add parameters to prevent SQL injection
                            command.Parameters.AddWithValue("@Sms_Text", SmSObj.Sms_Text ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_Mobile_Number", SmSObj.Sms_Mobile_Number ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_Queue_Date", SmSObj.Sms_Queue_Date ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_Sent_Date", SmSObj.Sms_Sent_Date ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_SentStatus", SmSObj.Sms_SentStatus);
                            command.Parameters.AddWithValue("@Template_ID", SmSObj.Template_ID);
                            command.Parameters.AddWithValue("@Parameter1_value", SmSObj.Parameter1_value ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Parameter2_value", SmSObj.Parameter2_value ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Parameter3_value", SmSObj.Parameter3_value ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Parameter4_value", SmSObj.Parameter4_value ?? (object)DBNull.Value);

                            // Open connection and execute the query
                            connection.Open();
                            command.ExecuteNonQuery();
                        }
                    }

                }
                else if (ContactPersonMobile != "" && (IsDealer == true))
                {
                    ProductType = productTypeList.Where(P => P.ProductType_ID == ServiceRequest.ProductType_ID).Select(S => S.ProductType_Name).FirstOrDefault();
                    WF_Sms SmSObj = new WF_Sms();
                    SmSObj.Sms_Text = "Your request is registered with Call Number " + ServiceRequestNumber + ", Product Type:" + ProductType + ". We will get in touch with you shortly.";
                    SmSObj.Sms_Mobile_Number = ContactPersonMobile;
                    SmSObj.Sms_Queue_Date = localTime;  //Added by Venkateshwari for HelpDesk CR-5 Changes 13-Aug-2015.
                    SmSObj.Sms_Sent_Date = null;
                    SmSObj.Sms_SentStatus = false;
                    SmSObj.Template_ID = 1;
                    if (ProductType != "")
                    {
                        SmSObj.Parameter1_value = ServiceRequestNumber + ", " + ProductType + " ";
                    }
                    else
                    {
                        SmSObj.Parameter1_value = ServiceRequestNumber + " ";
                    }
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        string query = @"
                            INSERT INTO WF_Sms 
                                (Sms_Text, Sms_Mobile_Number, Sms_Queue_Date, Sms_Sent_Date, 
                                 Sms_SentStatus, Template_ID, Parameter1_value, Parameter2_value, 
                                 Parameter3_value, Parameter4_value) 
                            VALUES 
                                (@Sms_Text, @Sms_Mobile_Number, @Sms_Queue_Date, @Sms_Sent_Date, 
                                 @Sms_SentStatus, @Template_ID, @Parameter1_value, @Parameter2_value, 
                                 @Parameter3_value, @Parameter4_value);
                        ";

                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            // Add parameters to prevent SQL injection
                            command.Parameters.AddWithValue("@Sms_Text", SmSObj.Sms_Text ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_Mobile_Number", SmSObj.Sms_Mobile_Number ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_Queue_Date", SmSObj.Sms_Queue_Date ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_Sent_Date", SmSObj.Sms_Sent_Date ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Sms_SentStatus", SmSObj.Sms_SentStatus);
                            command.Parameters.AddWithValue("@Template_ID", SmSObj.Template_ID);
                            command.Parameters.AddWithValue("@Parameter1_value", SmSObj.Parameter1_value ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Parameter2_value", SmSObj.Parameter2_value ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Parameter3_value", SmSObj.Parameter3_value ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@Parameter4_value", SmSObj.Parameter4_value ?? (object)DBNull.Value);

                            // Open connection and execute the query
                            connection.Open();
                            command.ExecuteNonQuery();
                        }
                    }

                }
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        #region :::  CommonMethodForEmailandSMS Uday Kumar J B 13-08-2024 not Used I think but check once:::
        /// <summary>
        /// To fetch Email Subject, Boday & SMS
        /// </summary> 
        /// 
        public  StringBuilder[] CommonMethodForEmailandSMS(string connString, CommonMethodForEmailandSMSList CommonMethodForEmailandSMSobj)
        {
            int LogException = Convert.ToInt32(_configuration["LogError"]);
            int BranchId = CommonMethodForEmailandSMSobj?.BranchId ?? 0;

            string p1 = CommonMethodForEmailandSMSobj?.p1 ?? "";
            string p2 = CommonMethodForEmailandSMSobj?.p2 ?? "";
            string p3 = CommonMethodForEmailandSMSobj?.p3 ?? "";
            string p4 = CommonMethodForEmailandSMSobj?.p4 ?? "";
            string p5 = CommonMethodForEmailandSMSobj?.p5 ?? "";
            string p6 = CommonMethodForEmailandSMSobj?.p6 ?? "";
            string p7 = CommonMethodForEmailandSMSobj?.p7 ?? "";
            string p8 = CommonMethodForEmailandSMSobj?.p8 ?? "";
            string p9 = CommonMethodForEmailandSMSobj?.p9 ?? "";
            string p10 = CommonMethodForEmailandSMSobj?.p10 ?? "";
            string p11 = CommonMethodForEmailandSMSobj?.p11 ?? "";
            string p12 = CommonMethodForEmailandSMSobj?.p12 ?? "";
            string p13 = CommonMethodForEmailandSMSobj?.p13 ?? "";
            string p14 = CommonMethodForEmailandSMSobj?.p14 ?? "";
            string p15 = CommonMethodForEmailandSMSobj?.p15 ?? "";
            string p16 = CommonMethodForEmailandSMSobj?.p16 ?? "";
            string p17 = CommonMethodForEmailandSMSobj?.p17 ?? "";
            string p18 = CommonMethodForEmailandSMSobj?.p18 ?? "";
            string p19 = CommonMethodForEmailandSMSobj?.p19 ?? "";
            string p20 = CommonMethodForEmailandSMSobj?.p20 ?? "";

            StringBuilder[] Result = new StringBuilder[5];

            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectEmailTemplateGmail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@TemplateCode", CommonMethodForEmailandSMSobj.TemplateCode);
                        command.Parameters.AddWithValue("@CompanyId", CommonMethodForEmailandSMSobj.CompanyId);
                        command.Parameters.AddWithValue("@LanguageCode", CommonMethodForEmailandSMSobj.LanguageCode);
                        command.Parameters.AddWithValue("@BranchId", BranchId);
                        command.Parameters.AddWithValue("@Param1", p1);
                        command.Parameters.AddWithValue("@Param2", p2);
                        command.Parameters.AddWithValue("@Param3", p3);
                        command.Parameters.AddWithValue("@Param4", p4);
                        command.Parameters.AddWithValue("@Param5", p5);
                        command.Parameters.AddWithValue("@Param6", p6);
                        command.Parameters.AddWithValue("@Param7", p7);
                        command.Parameters.AddWithValue("@Param8", p8);
                        command.Parameters.AddWithValue("@Param9", p9);
                        command.Parameters.AddWithValue("@Param10", p10);
                        command.Parameters.AddWithValue("@Param11", p11);
                        command.Parameters.AddWithValue("@Param12", p12);
                        command.Parameters.AddWithValue("@Param13", p13);
                        command.Parameters.AddWithValue("@Param14", p14);
                        command.Parameters.AddWithValue("@Param15", p15);
                        command.Parameters.AddWithValue("@Param16", p16);
                        command.Parameters.AddWithValue("@Param17", p17);
                        command.Parameters.AddWithValue("@Param18", p18);
                        command.Parameters.AddWithValue("@Param19", p19);
                        command.Parameters.AddWithValue("@Param20", p20);


                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Result[0] = new StringBuilder(reader["Subject"].ToString());
                                Result[1] = new StringBuilder(reader["Body"].ToString());
                                Result[2] = new StringBuilder(reader["SMS"].ToString());
                                Result[3] = new StringBuilder(reader["BCC"].ToString());
                                Result[4] = new StringBuilder(reader["CC"].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                throw ex;
            }

            return (Result);
        }
        #endregion
    }
}