
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35027.167
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HCLSoftware_AMERP_API_Standalone", "HCLSoftware_DPC_API_Standalone\HCLSoftware_AMERP_API_Standalone.csproj", "{84C9CD06-DFE7-4D9B-8A02-27CD527864DB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SharedAPIClassLibrary_AMERP", "SharedAPIClassLibrary_DC\SharedAPIClassLibrary_AMERP.csproj", "{E1DADF95-1DA0-4B88-99B8-3EC80FD5B7B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FunctionApp_AMERP", "FunctionApp_DPC\FunctionApp_AMERP.csproj", "{769DE737-0625-4ED3-8BA9-80F7519CDD76}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PBC.CoreService", "PBC.CoreService\PBC.CoreService.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PBC.UtilityService", "PBC.UtilityService\PBC.UtilityService.csproj", "{A6D86035-C809-4255-8AA0-B418B834A047}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PBC.AggregatorService", "PBC.AggregatorService\PBC.AggregatorService.csproj", "{EE653325-7149-A6F7-8EB4-EDA092191E47}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PBC.WorkflowService", "PBC.WorkflowService\PBC.WorkflowService.csproj", "{94698CD8-AF5B-ED37-E3F4-0F9F95AB1F75}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WorkFlow", "WorkFlow\WorkFlow.csproj", "{611E356A-C847-47A8-BE7C-49745491783E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PBC.HelpdeskService", "PBC.HelpdeskService\PBC.HelpdeskService.csproj", "{8E9539BC-F5B9-07C8-5A3D-9640DFCED22D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{84C9CD06-DFE7-4D9B-8A02-27CD527864DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84C9CD06-DFE7-4D9B-8A02-27CD527864DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84C9CD06-DFE7-4D9B-8A02-27CD527864DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84C9CD06-DFE7-4D9B-8A02-27CD527864DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1DADF95-1DA0-4B88-99B8-3EC80FD5B7B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1DADF95-1DA0-4B88-99B8-3EC80FD5B7B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1DADF95-1DA0-4B88-99B8-3EC80FD5B7B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1DADF95-1DA0-4B88-99B8-3EC80FD5B7B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{769DE737-0625-4ED3-8BA9-80F7519CDD76}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{769DE737-0625-4ED3-8BA9-80F7519CDD76}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{769DE737-0625-4ED3-8BA9-80F7519CDD76}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{769DE737-0625-4ED3-8BA9-80F7519CDD76}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A6D86035-C809-4255-8AA0-B418B834A047}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A6D86035-C809-4255-8AA0-B418B834A047}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A6D86035-C809-4255-8AA0-B418B834A047}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A6D86035-C809-4255-8AA0-B418B834A047}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE653325-7149-A6F7-8EB4-EDA092191E47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE653325-7149-A6F7-8EB4-EDA092191E47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE653325-7149-A6F7-8EB4-EDA092191E47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE653325-7149-A6F7-8EB4-EDA092191E47}.Release|Any CPU.Build.0 = Release|Any CPU
		{94698CD8-AF5B-ED37-E3F4-0F9F95AB1F75}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94698CD8-AF5B-ED37-E3F4-0F9F95AB1F75}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94698CD8-AF5B-ED37-E3F4-0F9F95AB1F75}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94698CD8-AF5B-ED37-E3F4-0F9F95AB1F75}.Release|Any CPU.Build.0 = Release|Any CPU
		{611E356A-C847-47A8-BE7C-49745491783E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{611E356A-C847-47A8-BE7C-49745491783E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{611E356A-C847-47A8-BE7C-49745491783E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{611E356A-C847-47A8-BE7C-49745491783E}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E9539BC-F5B9-07C8-5A3D-9640DFCED22D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E9539BC-F5B9-07C8-5A3D-9640DFCED22D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E9539BC-F5B9-07C8-5A3D-9640DFCED22D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E9539BC-F5B9-07C8-5A3D-9640DFCED22D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1B2AC710-5FA9-4C4E-9AB7-52A7B2210E16}
	EndGlobalSection
EndGlobal
