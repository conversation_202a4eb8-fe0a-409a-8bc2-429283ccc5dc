using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PBC.Core_SharedAPIClass.Models;
//using SharedAPIClassLibrary_AMERP.Utilities;
//using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
//using WorkFlow.Models;
//using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace SharedAPIClassLibrary_AMERP
{
    public class CoreEmailTemplateService
    {

        #region :::  CommonMethodForEmailandSMS :::
        /// <summary>
        /// To fetch Email Subject, Boday & SMS
        /// </summary> 
        /// 
        public static StringBuilder[] CommonMethodForEmailandSMS(string connString, CommonMethodForEmailandSMSList CommonMethodForEmailandSMSobj)
        {
            int LogException = 1;//Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int BranchId = CommonMethodForEmailandSMSobj?.BranchId ?? 0;

            string p1 = CommonMethodForEmailandSMSobj?.p1 ?? "";
            string p2 = CommonMethodForEmailandSMSobj?.p2 ?? "";
            string p3 = CommonMethodForEmailandSMSobj?.p3 ?? "";
            string p4 = CommonMethodForEmailandSMSobj?.p4 ?? "";
            string p5 = CommonMethodForEmailandSMSobj?.p5 ?? "";
            string p6 = CommonMethodForEmailandSMSobj?.p6 ?? "";
            string p7 = CommonMethodForEmailandSMSobj?.p7 ?? "";
            string p8 = CommonMethodForEmailandSMSobj?.p8 ?? "";
            string p9 = CommonMethodForEmailandSMSobj?.p9 ?? "";
            string p10 = CommonMethodForEmailandSMSobj?.p10 ?? "";
            string p11 = CommonMethodForEmailandSMSobj?.p11 ?? "";
            string p12 = CommonMethodForEmailandSMSobj?.p12 ?? "";
            string p13 = CommonMethodForEmailandSMSobj?.p13 ?? "";
            string p14 = CommonMethodForEmailandSMSobj?.p14 ?? "";
            string p15 = CommonMethodForEmailandSMSobj?.p15 ?? "";
            string p16 = CommonMethodForEmailandSMSobj?.p16 ?? "";
            string p17 = CommonMethodForEmailandSMSobj?.p17 ?? "";
            string p18 = CommonMethodForEmailandSMSobj?.p18 ?? "";
            string p19 = CommonMethodForEmailandSMSobj?.p19 ?? "";
            string p20 = CommonMethodForEmailandSMSobj?.p20 ?? "";

            StringBuilder[] Result = new StringBuilder[5];

            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectEmailTemplateGmail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@TemplateCode", CommonMethodForEmailandSMSobj.TemplateCode);
                        command.Parameters.AddWithValue("@CompanyId", CommonMethodForEmailandSMSobj.CompanyId);
                        command.Parameters.AddWithValue("@LanguageCode", CommonMethodForEmailandSMSobj.LanguageCode);
                        command.Parameters.AddWithValue("@BranchId", BranchId);
                        command.Parameters.AddWithValue("@Param1", p1);
                        command.Parameters.AddWithValue("@Param2", p2);
                        command.Parameters.AddWithValue("@Param3", p3);
                        command.Parameters.AddWithValue("@Param4", p4);
                        command.Parameters.AddWithValue("@Param5", p5);
                        command.Parameters.AddWithValue("@Param6", p6);
                        command.Parameters.AddWithValue("@Param7", p7);
                        command.Parameters.AddWithValue("@Param8", p8);
                        command.Parameters.AddWithValue("@Param9", p9);
                        command.Parameters.AddWithValue("@Param10", p10);
                        command.Parameters.AddWithValue("@Param11", p11);
                        command.Parameters.AddWithValue("@Param12", p12);
                        command.Parameters.AddWithValue("@Param13", p13);
                        command.Parameters.AddWithValue("@Param14", p14);
                        command.Parameters.AddWithValue("@Param15", p15);
                        command.Parameters.AddWithValue("@Param16", p16);
                        command.Parameters.AddWithValue("@Param17", p17);
                        command.Parameters.AddWithValue("@Param18", p18);
                        command.Parameters.AddWithValue("@Param19", p19);
                        command.Parameters.AddWithValue("@Param20", p20);


                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Result[0] = new StringBuilder(reader["Subject"].ToString());
                                Result[1] = new StringBuilder(reader["Body"].ToString());
                                Result[2] = new StringBuilder(reader["SMS"].ToString());
                                Result[3] = new StringBuilder(reader["BCC"].ToString());
                                Result[4] = new StringBuilder(reader["CC"].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    //LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                throw ex;
            }

            return (Result);
        }
        #endregion


    }
}
