using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PBC.HelpdeskService.Services
{
    /// <summary>
    /// Interface for communicating with PBC.UtilityService
    /// </summary>
    public interface IUtilityServiceClient
    {
        /// <summary>
        /// Decrypt string using utility service
        /// </summary>
        Task<string> DecryptStringAsync(string encryptedString);

        /// <summary>
        /// Get resource string using utility service
        /// </summary>
        Task<string> GetResourceStringAsync(string cultureValue, string resourceKey);

        /// <summary>
        /// Get initial setup data using utility service
        /// </summary>
        Task<JsonResult> GetInitialSetupAsync(int objectId, int userId, string connectionString, int logException);

        /// <summary>
        /// Get object ID using utility service
        /// </summary>
        Task<int> GetObjectIdAsync(string name, string connectionString, int logException);

        /// <summary>
        /// Check permissions using utility service
        /// </summary>
        Task<bool> CheckPermissionsAsync(string name, string wfName, string helpDesk, int companyId, int logException, int userId, string connectionString);

        /// <summary>
        /// Get value from database using utility service
        /// </summary>
        Task<string> GetValueFromDbAsync(string query, Dictionary<string, object> parameters, string connectionString);

        /// <summary>
        /// Get workflow ID using utility service
        /// </summary>
        Task<int> GetWorkFlowIDAsync(string workFlowName, string dbName, string connectionString, int logException);

        /// <summary>
        /// Check if user can add records using utility service
        /// </summary>
        Task<bool> CheckIsAddRecordsAsync(int objectId, int workFlowId, int companyId, int userId, string connectionString, int logException);

        /// <summary>
        /// Get group queue for workflow using utility service
        /// </summary>
        Task<List<dynamic>> GetGroupQueueAsync(int companyId, string connectionString, int workFlowId, int userId);

        /// <summary>
        /// Get all queue for workflow using utility service
        /// </summary>
        Task<List<dynamic>> GetAllQueueAsync(int companyId, int workFlowId, int userId, int statusId, int branchId, string connectionString, int logException);

        /// <summary>
        /// Get end step status ID for workflow using utility service
        /// </summary>
        Task<int> GetEndStepStatusIDAsync(int workflowId, string connectionString, int logException);

        /// <summary>
        /// Check if party-specific service level agreement exists using utility service
        /// </summary>
        Task<bool> CheckPartySpecificAsync(int partyId, int callComplexityId, int? callPriorityId, int companyId, string connectionString, int logException);

        /// <summary>
        /// Calculate working hours for a given date and company using utility service
        /// </summary>
        Task<double> GetWorkingHoursAsync(DateTime callDate, int companyId, string connectionString, int logException);

        /// <summary>
        /// Check if user is admin for a workflow using utility service
        /// </summary>
        Task<bool> CheckForAdminAsync(int userId, string workFlowName, string dbName, string connectionString, int logException);

        /// <summary>
        /// Check if auto allocation is allowed for a workflow using utility service
        /// </summary>
        Task<bool> CheckAutoAllocationAsync(int companyId, int workFlowId, int userId, string connectionString, int logException);

        /// <summary>
        /// Get auto allocation step details for a workflow using utility service
        /// </summary>
        Task<object> GetAutoAllocationStepDetailsAsync(int workFlowId, int companyId, string connectionString, int logException);

        /// <summary>
        /// Get workflow role condition for user using utility service
        /// </summary>
        Task<string> GetGrpQinconditionAsync(int userId, string connectionString, int logException);

        /// <summary>
        /// Get workflow status IDs for workflow management using utility service
        /// </summary>
        Task<string> GetStatusIDsAsync(int statusId, int workFlowId, string connectionString, int logException);

        /// <summary>
        /// Get service request query for help desk operations using utility service
        /// </summary>
        Task<string> GetServiceRequestQueryAsync(string connectionString, int logException, int langID,
            string genLangCode, string userLangCode, int mode, int userId, int companyId, int branchId,
            string sidx, string sord, string dbName);

        /// <summary>
        /// Lock a record in the workflow system using utility service
        /// </summary>
        Task<string> LockRecordAsync(string connString, int logException, string userCulture, int quotationID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0);

        /// <summary>
        /// Unlock a record in the workflow system using utility service
        /// </summary>
        Task<string> UnLockRecordAsync(string connString, int logException, string userCulture, int jobcardID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0);

        /// <summary>
        /// Convert server time to local time based on branch timezone using utility service
        /// </summary>
        Task<DateTime> LocalTimeBasedOnBranchAsync(int branchID, DateTime serverTime, string connString);

        /// <summary>
        /// Convert server time to local time based on user timezone using utility service
        /// </summary>
        Task<DateTime> LocalTimeAsync(int userID, DateTime serverTime, string connString);

        /// <summary>
        /// Get the end step status name for a workflow using utility service
        /// </summary>
        Task<string> GetEndStepStatusNameAsync(int workflowID, string connString, int logException);

        /// <summary>
        /// Order data by a specified field and sort direction using utility service
        /// </summary>
        Task<ExtensionMethodsResponse<object[]>> OrderByFieldAsync(string sortField, string sortDirection, object[] data);

        /// <summary>
        /// Filter data using simple filter rules using utility service
        /// </summary>
        Task<ExtensionMethodsResponse<object[]>> FilterSearchAsync(Filters filters, object[] data);

        // WorkflowService functions

        /// <summary>
        /// Check if prefix/suffix configuration exists for the given parameters using workflow service
        /// </summary>
        Task<bool> CheckPrefixSuffixAsync(int companyID, int branchID, string objectName, string dbName, string connectionString);

        /// <summary>
        /// Get object ID by object name using workflow service
        /// </summary>
        Task<int> GetWorkflowObjectIDAsync(string name, string dbName, string connectionString);
        Task<IActionResult> CommonMethodForEmailandSMS(CommonMethodForEmailandSMSList obj, string connString, int logException);

    }
}
