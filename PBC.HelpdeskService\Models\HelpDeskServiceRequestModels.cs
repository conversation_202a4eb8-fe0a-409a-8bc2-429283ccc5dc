﻿namespace PBC.HelpdeskService.Models
{


    public class InsertServiceRequestNegativeFeedbackEmailsList
    {
        public string RequestNumber { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string Customer { get; set; }
        public int Model_Id { get; set; }
        public string ProductType { get; set; }
        public string SerialNumber { get; set; }
        public string ContactPerson { get; set; }
        public string MobileNumber { get; set; }
        public string CallDescription { get; set; }
        public string CustomerRating { get; set; }
        public string Feedback { get; set; }
    }

public partial class GNM_Company
        {
            public GNM_Company()
            {
                this.GNM_Company_Company_Relation = new HashSet<GNM_Company_Company_Relation>();
                this.GNM_Company_Company_Relation1 = new HashSet<GNM_Company_Company_Relation>();
                this.GNM_CompanyBrands = new HashSet<GNM_CompanyBrands>();
                this.GNM_CompanyLocale = new HashSet<GNM_CompanyLocale>();
                this.GNM_Branch = new HashSet<GNM_Branch>();
                this.GNM_CompanyFinancialYear = new HashSet<GNM_CompanyFinancialYear>();
                this.GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>();
                this.GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>();
                this.GNM_HourlyRate = new HashSet<GNM_HourlyRate>();
                this.GNM_CompanyEmployee = new HashSet<GNM_CompanyEmployee>();
            }

            public int Company_ID { get; set; }
            public string Company_Name { get; set; }
            public string Company_ShortName { get; set; }
            public int Currency_ID { get; set; }
            public string Company_Address { get; set; }
            public string Company_Type { get; set; }
            public bool Company_Active { get; set; }
            public string Company_LogoName { get; set; }
            public Nullable<int> Company_Parent_ID { get; set; }
            public string Remarks { get; set; }
            public byte DefaultGridSize { get; set; }
            public Nullable<decimal> JobCardCushionHours { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> CompanyTheme_ID { get; set; }
            public Nullable<int> QuotationValidity { get; set; }
            public string CompanyFont { get; set; }
            public Nullable<decimal> InventoryCarryingFactoy_Percentage { get; set; }
            public Nullable<int> OrderingCost { get; set; }

            public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation { get; set; }
            public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation1 { get; set; }
            public virtual ICollection<GNM_CompanyBrands> GNM_CompanyBrands { get; set; }
            public virtual ICollection<GNM_CompanyLocale> GNM_CompanyLocale { get; set; }
            public virtual ICollection<GNM_Branch> GNM_Branch { get; set; }
            public virtual ICollection<GNM_CompanyFinancialYear> GNM_CompanyFinancialYear { get; set; }
            public virtual ICollection<GNM_HEADERFOOTERPRINT> GNM_HEADERFOOTERPRINT { get; set; }
            public virtual ICollection<GNM_TERMSANDCONDITIONS> GNM_TERMSANDCONDITIONS { get; set; }
            public virtual ICollection<GNM_HourlyRate> GNM_HourlyRate { get; set; }
            public virtual ICollection<GNM_CompanyEmployee> GNM_CompanyEmployee { get; set; }
        }
    public partial class GNM_ProductReading
    {
        public int ProductReadingID { get; set; }
        public Nullable<int> Product_ID { get; set; }
        public Nullable<int> ProductComponent_ID { get; set; }
        public int Mode { get; set; }
        public int Company_ID { get; set; }
        public string Reference_Number { get; set; }
        public System.DateTime Reference_Date { get; set; }
        public int Reading { get; set; }
        public Nullable<int> JobCardId { get; set; }
        public Nullable<decimal> AccumulatedHMR { get; set; }

        public virtual GNM_Product GNM_Product { get; set; }
        public virtual GNM_ProductComponent GNM_ProductComponent { get; set; }
    }
    public partial class GNM_ProductComponent
    {
        public GNM_ProductComponent()
        {
            this.GNM_ProductReading = new HashSet<GNM_ProductReading>();
        }

        public int ProductComponent_ID { get; set; }
        public int Product_ID { get; set; }
        public int Parts_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public string ProductComponent_SerialNumber { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> Reading { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> ExpectedLife { get; set; }
        public Nullable<int> ReManCycle { get; set; }
        public string UsageArea { get; set; }

        public virtual GNM_Product GNM_Product { get; set; }
        public virtual ICollection<GNM_ProductReading> GNM_ProductReading { get; set; }
    }

    public class CommonMethodForEmailandSMSList
    {
        public string TemplateCode { get; set; }
        public int CompanyId { get; set; }
        public string LanguageCode { get; set; }
        public int BranchId { get; set; }
        public string p1 { get; set; }
        public string p2 { get; set; }
        public string p3 { get; set; }
        public string p4 { get; set; }
        public string p5 { get; set; }
        public string p6 { get; set; }
        public string p7 { get; set; }
        public string p8 { get; set; }
        public string p9 { get; set; }
        public string p10 { get; set; }
        public string p11 { get; set; }
        public string p12 { get; set; }
        public string p13 { get; set; }
        public string p14 { get; set; }
        public string p15 { get; set; }
        public string p16 { get; set; }
        public string p17 { get; set; }
        public string p18 { get; set; }
        public string p19 { get; set; }
        public string p20 { get; set; }
    }

    public class WF_WFField
    {
        public int WFField_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WorkFlowFieldName { get; set; }

        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

        public WF_WFField()
        {
            GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
        }
    }

    public partial class GNM_ProductStatusHistory
    {
        public int ProductServiceHistory_ID { get; set; }
        public int Product_ID { get; set; }
        public byte Mode { get; set; }
        public int Company_ID { get; set; }
        public System.DateTime ProductStatus_Date { get; set; }
        public string MobileNumber { get; set; }
        public int MachineStatus_ID { get; set; }

        public virtual GNM_Product GNM_Product { get; set; }
    }
    public partial class HD_UnregisteredServiceRequest
    {
        public int UnregisteredServiceRequest_ID { get; set; }
        public int Company_ID { get; set; }
        public string Product_Unique_Number { get; set; }
        public int Party_ID { get; set; }
        public string RequestDescription { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public string SerialNumber { get; set; }
        public string Email_ID { get; set; }
        public string Mobile { get; set; }
        public string Phone { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<int> ServiceRequest_ID { get; set; }
        public string Remarks { get; set; }
        public byte Status { get; set; }
        public Nullable<int> Locked_by_User_ID { get; set; }
        public Nullable<decimal> Coordinate_Latitude { get; set; }
        public Nullable<decimal> Coordinate_Longitude { get; set; }
        public string LatLongAddress { get; set; }
    }
    public class WF_WFActionLocale
    {
        public int WFActionLocale_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int WFAction_ID { get; set; }

        public int Language_ID { get; set; }

        public string WFAction_Name { get; set; }

        public string ActionCode { get; set; }

        public virtual WF_WFAction GNM_WFAction { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }

    public class WF_WFAction
    {
        public int WFAction_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WFAction_Name { get; set; }

        public string ActionCode { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }

        public WF_WFAction()
        {
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }
    }

    public class WF_WFStepLink
    {
        public int WFStepLink_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int Company_ID { get; set; }

        public int FrmWFSteps_ID { get; set; }

        public int WFAction_ID { get; set; }

        public int ToWFSteps_ID { get; set; }

        public int? Addresse_WFRole_ID { get; set; }

        public byte Addresse_Flag { get; set; }

        public bool IsSMSSentToCustomer { get; set; }

        public bool IsEmailSentToCustomer { get; set; }

        public bool IsSMSSentToAddressee { get; set; }

        public bool IsEmailSentToAddresse { get; set; }

        public bool AutoAllocationAllowed { get; set; }

        public bool IsVersionEnabled { get; set; }

        public int? InvokeParentWF_ID { get; set; }

        public int? InvokeParentWFLink_ID { get; set; }

        public int? InvokeChildObject_ID { get; set; }

        public int? InvokeChildObjectAction { get; set; }

        public int? WFField_ID { get; set; }

        public string AutoCondition { get; set; }

        public virtual WF_WFAction GNM_WFAction { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; }

        public virtual WF_WFSteps GNM_WFSteps { get; set; }

        public virtual WF_WFSteps GNM_WFSteps1 { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }

    public class WF_WFRole
    {
        public int WFRole_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WFRole_Name { get; set; }

        public bool WfRole_IsAdmin { get; set; }

        public bool WfRole_AutoAllocationAllowed { get; set; }

        public bool? WFRole_IsRoleExternal { get; set; }

        public int? WFRole_ExternalCompany_ID { get; set; }

        public virtual ICollection<WF_WFRoleUser> GNM_WFRoleUser { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFRoleLocale> GNM_WFRoleLocale { get; set; }

        public WF_WFRole()
        {
            GNM_WFRoleUser = new HashSet<WF_WFRoleUser>();
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFRoleLocale = new HashSet<WF_WFRoleLocale>();
        }
    }

    public class WF_WFRoleUser
    {
        public int WFRoleUser_ID { get; set; }

        public int WFRole_ID { get; set; }

        public int UserID { get; set; }

        public int ApprovalLimit { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; }
    }

    public class WF_WFRoleLocale
    {
        public int WFRoleLocale_ID { get; set; }

        public int WFRole_ID { get; set; }

        public string WFRole_Name { get; set; }

        public int Language_ID { get; set; }

        public virtual WF_WFRole GNM_WFRole { get; set; }
    }

    public class WF_WFSteps
    {
        public int WFSteps_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WFStep_Name { get; set; }

        public int WFStepType_ID { get; set; }

        public int WFStepStatus_ID { get; set; }

        public bool WFStep_IsActive { get; set; }

        public string BranchCode { get; set; }

        public virtual WF_WFStepType GNM_WFStepType { get; set; }

        public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink1 { get; set; }

        public virtual ICollection<WF_WFStepsLocale> GNM_WFStepsLocale { get; set; }

        public WF_WFSteps()
        {
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFStepLink1 = new HashSet<WF_WFStepLink>();
            GNM_WFStepsLocale = new HashSet<WF_WFStepsLocale>();
        }
    }
    public class WF_WFStepsLocale
    {
        public int WFStepsLocale_ID { get; set; }

        public int WFSteps_ID { get; set; }

        public string WFStep_Name { get; set; }

        public int Language_ID { get; set; }

        public virtual WF_WFSteps GNM_WFSteps { get; set; }
    }
    public class WF_WFStepType
    {
        public int WFStepType_ID { get; set; }

        public string WFStepType_Nm { get; set; }

        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }

        public WF_WFStepType()
        {
            GNM_WFSteps = new HashSet<WF_WFSteps>();
        }
    }
    public class WF_WorkFlow
    {
        public int WorkFlow_ID { get; set; }

        public string WorkFlow_Name { get; set; }

        public bool? AllQueue_Filter_IsBranch { get; set; }

        public virtual ICollection<WF_WFAction> GNM_WFAction { get; set; }

        public virtual ICollection<WF_WFField> GNM_WFField { get; set; }

        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

        public virtual ICollection<WF_WFRole> GNM_WFRole { get; set; }

        public virtual ICollection<WF_WFSteps> GNM_WFSteps { get; set; }

        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }

        public virtual ICollection<WF_WFActionLocale> GNM_WFActionLocale { get; set; }

        public WF_WorkFlow()
        {
            GNM_WFAction = new HashSet<WF_WFAction>();
            GNM_WFField = new HashSet<WF_WFField>();
            GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
            GNM_WFRole = new HashSet<WF_WFRole>();
            GNM_WFSteps = new HashSet<WF_WFSteps>();
            GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            GNM_WFActionLocale = new HashSet<WF_WFActionLocale>();
        }
    }

    public class WF_WFFieldValue
    {
        public int WFFieldValue_ID { get; set; }

        public int WFField_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public int Company_ID { get; set; }

        public int Transaction_ID { get; set; }

        public string WorkFlowFieldValue { get; set; }

        public virtual WF_WFField GNM_WFField { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }

    public class SMSTemplate
    {
        public int Template_ID { get; set; }

        public string Param1 { get; set; }

        public string Param2 { get; set; }

        public string Param3 { get; set; }

        public string Param4 { get; set; }
    }
    public class Attachements
    {
        public int ATTACHMENTDETAIL_ID
        {
            get;
            set;
        }
        public string AttachmentIDS { get; set; }
        public int TransactionID { get; set; }
        public string FILE_NAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public string UPLOADBY { get; set; }
        public DateTime UPLOADDATE { get; set; }
        public string UPLOADDATESORT { get; set; }
        public string delete { get; set; }
        public int Upload { get; set; }
        public string view { get; set; }
        public int OBJECTID { get; set; }
        public string DocumentType { get; set; }
        public int DocumentType_ID { get; set; }
        public int? DetailID { get; set; }
        public int ID { get; set; }
        public string edit { get; set; }
        public string Tablename { get; set; }

        public int PartID { get; set; }
        public string TransactionType { get; set; }
        public string TransactionNumber { get; set; }
        public string Date { get; set; }
        public string Amount { get; set; }
        public int ModelID { get; set; }
        public string Type { get; set; }
        public string Remarks { get; set; }
    }

}
